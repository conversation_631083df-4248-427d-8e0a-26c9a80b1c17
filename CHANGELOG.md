# سجل التغييرات (Changelog)

جميع التغييرات المهمة في مشروع "دليل" سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/ar/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/lang/ar/).

---

## [1.0.0] - 2025-01-XX

### 🎉 الإصدار الأول

الإصدار الأول من نظام "دليل" للملاحة الأوفلاين.

### ✨ الميزات المضافة

#### نظام الخرائط
- إضافة خريطة تفاعلية باستخدام Leaflet.js
- دعم التكبير والتصغير والتنقل
- عرض خريطة المملكة العربية السعودية
- دعم الخرائط من OpenStreetMap

#### إدارة المواقع
- إضافة موقع جديد بالاسم والإحداثيات والوصف
- عرض قائمة المواقع المحفوظة
- تعديل المواقع الموجودة
- حذف المواقع
- عرض الموقع على الخريطة
- تحديد الموقع من الخريطة مباشرة

#### نظام التوجيه
- إنشاء مسار من الموقع الحالي إلى وجهة محددة
- حساب المسافة بالكيلومتر
- حساب الوقت المتوقع بالدقائق
- عرض المسار على الخريطة بخط ملون
- حفظ المسارات في قاعدة البيانات
- إلغاء المسار الحالي

#### تتبع GPS
- تتبع الموقع الحالي في الوقت الفعلي
- عرض خط العرض والطول
- عرض السرعة بالكيلومتر/ساعة
- عرض الاتجاه بالدرجات
- حفظ سجل التتبع
- تحديث الموقع على الخريطة تلقائياً

#### البحث
- البحث في المواقع المحفوظة
- البحث في قاعدة بيانات المدن السعودية (150+ مدينة)
- عرض نتائج البحث بشكل منظم
- الانتقال السريع للنتائج على الخريطة

#### إدارة البيانات
- تصدير المواقع بصيغة JSON
- استيراد المواقع من ملف JSON
- قاعدة بيانات SQLite محلية
- حفظ تلقائي لجميع البيانات

#### الواجهة
- تصميم عربي كامل (RTL)
- ألوان خضراء داكنة أنيقة
- واجهة متجاوبة مع جميع الأحجام
- رسوم متحركة سلسة
- أيقونات Font Awesome
- نظام إشعارات جميل

#### قاعدة بيانات المدن
- 150+ مدينة سعودية مع الإحداثيات
- تصنيف حسب المناطق (13 منطقة)
- دوال مساعدة للبحث والفلترة
- حساب أقرب مدينة

### 🛠️ التحسينات التقنية

#### Backend
- خادم Node.js + Express
- REST API كامل
- قاعدة بيانات SQLite
- معالجة أخطاء شاملة
- دعم CORS

#### Frontend
- JavaScript ES6+
- Async/Await للعمليات غير المتزامنة
- معالجة أخطاء شاملة
- تخزين مؤقت للبيانات
- تحديثات فورية للواجهة

#### قاعدة البيانات
- 4 جداول رئيسية
- Indexes للأداء
- Transactions للعمليات المتعددة
- Auto-increment للمفاتيح الأساسية

### 📚 التوثيق

- README.md شامل
- USAGE_GUIDE.md - دليل الاستخدام الكامل
- OFFLINE_MAPS_SETUP.md - دليل إعداد الخرائط الأوفلاين
- ARCHITECTURE.md - بنية المشروع التقنية
- DEPLOYMENT.md - دليل النشر
- CONTRIBUTING.md - دليل المساهمة
- CHANGELOG.md - سجل التغييرات
- LICENSE - رخصة MIT

### 🔧 أدوات التطوير

- setup.sh - سكريبت إعداد لـ Linux/Mac
- setup.bat - سكريبت إعداد لـ Windows
- sample-data.json - بيانات تجريبية (20 موقعاً)
- .gitignore - ملف تجاهل Git

### 🌐 المتصفحات المدعومة

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### 📦 المكتبات المستخدمة

#### Backend
- express: ^4.18.2
- better-sqlite3: ^9.2.2
- cors: ^2.8.5

#### Frontend
- Leaflet: 1.9.4
- Leaflet Routing Machine: 3.2.12
- Font Awesome: 6.4.0

---

## [Unreleased] - قيد التطوير

### 🔮 مخطط للإصدارات القادمة

#### الإصدار 1.1.0 (قريباً)

##### مخطط للإضافة
- [ ] دعم الخرائط الأوفلاين الكامل
- [ ] تكامل مع TileServer GL
- [ ] تحميل خرائط المناطق المحددة
- [ ] الملاحة الصوتية
- [ ] وضع الليل (Dark Mode)
- [ ] Progressive Web App (PWA)
- [ ] دعم التثبيت على الشاشة الرئيسية
- [ ] العمل بدون اتصال كامل

##### مخطط للتحسين
- [ ] تحسين أداء الخريطة
- [ ] تحسين استهلاك البطارية
- [ ] تحسين دقة GPS
- [ ] تحسين واجهة الجوال

#### الإصدار 1.2.0

##### مخطط للإضافة
- [ ] نقاط الاهتمام (POI)
  - [ ] محطات الوقود
  - [ ] المطاعم
  - [ ] المستشفيات
  - [ ] المساجد
- [ ] فئات للمواقع المحفوظة
- [ ] ألوان مخصصة للمواقع
- [ ] أيقونات مخصصة للمواقع

#### الإصدار 2.0.0

##### مخطط للإضافة
- [ ] دعم متعدد المستخدمين
- [ ] مشاركة المواقع بين المستخدمين
- [ ] التقارير والإحصائيات
- [ ] تصدير المسارات بصيغ متعددة (GPX, KML)
- [ ] المزامنة السحابية (اختيارية)
- [ ] تطبيق جوال (React Native)

---

## أنواع التغييرات

- `Added` - للميزات الجديدة
- `Changed` - للتغييرات في الميزات الموجودة
- `Deprecated` - للميزات التي ستُحذف قريباً
- `Removed` - للميزات المحذوفة
- `Fixed` - لإصلاح الأخطاء
- `Security` - للتحديثات الأمنية

---

## الروابط

- [الإصدار الحالي](https://github.com/your-repo/daleel/releases/tag/v1.0.0)
- [جميع الإصدارات](https://github.com/your-repo/daleel/releases)
- [المشاكل المفتوحة](https://github.com/your-repo/daleel/issues)
- [Pull Requests](https://github.com/your-repo/daleel/pulls)

---

**ملاحظة**: هذا المشروع في مرحلة التطوير النشط. نرحب بجميع المساهمات والاقتراحات!

