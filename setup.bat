@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ==========================================
echo      Bousla Navigation System Setup
echo ==========================================
echo.

REM Check if Node.js is installed
echo 🔍 Checking Node.js...
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js is not installed. Please install Node.js first from:
    echo    https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node -v') do set NODE_VERSION=%%i
echo ✅ Node.js installed: %NODE_VERSION%
echo.

REM Check if npm is available
echo 🔍 Checking npm...
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ npm is not installed
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm -v') do set NPM_VERSION=%%i
echo ✅ npm installed: %NPM_VERSION%
echo.

REM Install dependencies
echo 📦 Installing required dependencies...
call npm install

if %ERRORLEVEL% EQU 0 (
    echo ✅ Dependencies installed successfully
) else (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)
echo.

REM Create required directories
echo 📁 Creating directories...
if not exist "public\css" mkdir "public\css"
if not exist "public\js" mkdir "public\js"
if not exist "public\tiles" mkdir "public\tiles"
if not exist "data" mkdir "data"

echo ✅ Directories created
echo.

REM Check for essential files
echo 🔍 Checking essential files...

set ALL_FILES_EXIST=1

if exist "server.js" (
    echo   ✅ server.js
) else (
    echo   ❌ server.js not found
    set ALL_FILES_EXIST=0
)

if exist "public\index.html" (
    echo   ✅ public\index.html
) else (
    echo   ❌ public\index.html not found
    set ALL_FILES_EXIST=0
)

if exist "public\css\style.css" (
    echo   ✅ public\css\style.css
) else (
    echo   ❌ public\css\style.css not found
    set ALL_FILES_EXIST=0
)

if exist "public\js\app.js" (
    echo   ✅ public\js\app.js
) else (
    echo   ❌ public\js\app.js not found
    set ALL_FILES_EXIST=0
)

if exist "public\js\saudi-cities.js" (
    echo   ✅ public\js\saudi-cities.js
) else (
    echo   ❌ public\js\saudi-cities.js not found
    set ALL_FILES_EXIST=0
)

if %ALL_FILES_EXIST% EQU 0 (
    echo.
    echo ❌ Some essential files are missing
    pause
    exit /b 1
)
echo.

REM Setup database
echo 💾 Setting up database...
if exist "bousla.db" (
    echo ⚠️  Database already exists
    set /p REPLY="Do you want to delete it and create a new one? (y/n): "
    if /i "!REPLY!"=="y" (
        del bousla.db
        echo ✅ Old database deleted
    )
) else (
    echo ✅ Database will be created on first run
)
echo.

REM Get IP address
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    set IP=%%a
    set IP=!IP:~1!
    goto :ip_found
)
:ip_found

REM Display setup information
echo ==========================================
echo    ✅ Setup completed successfully!
echo ==========================================
echo.
echo 📋 Next steps:
echo.
echo 1️⃣  To start the server:
echo    npm start
echo.
echo 2️⃣  Open your browser at:
echo    http://localhost:3000
echo.
echo 3️⃣  For network access:
if defined IP (
    echo    http://!IP!:3000
) else (
    echo    Use: ipconfig to get your IP address
)
echo.
echo 4️⃣  For offline maps setup:
echo    See file: OFFLINE_MAPS_SETUP.md
echo.
echo 5️⃣  For complete user guide:
echo    See file: USAGE_GUIDE.md
echo.
echo ==========================================
echo    🧭 Enjoy using Bousla Navigation!
echo ==========================================
echo.

pause

