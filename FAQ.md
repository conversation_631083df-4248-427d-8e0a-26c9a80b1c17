# ❓ الأسئلة الشائعة (FAQ) - نظام دليل

## 📋 جدول المحتويات

- [عام](#-عام)
- [التثبيت والإعداد](#-التثبيت-والإعداد)
- [الاستخدام](#-الاستخدام)
- [الخرائط](#️-الخرائط)
- [المشاكل التقنية](#-المشاكل-التقنية)
- [الأمان والخصوصية](#-الأمان-والخصوصية)
- [التطوير](#-التطوير)

---

## 🌟 عام

### ما هو نظام دليل؟

**دليل** هو نظام ملاحة أوفلاين مصمم خصيصاً للمملكة العربية السعودية. يعمل بدون اتصال بالإنترنت ويوفر جميع الميزات الأساسية لأنظمة الملاحة الحديثة.

### هل النظام مجاني؟

نعم، النظام مفتوح المصدر ومجاني بالكامل تحت رخصة MIT.

### هل يعمل بدون إنترنت فعلاً؟

نعم، بعد إعداد الخرائط الأوفلاين (راجع [OFFLINE_MAPS_SETUP.md](OFFLINE_MAPS_SETUP.md))، يعمل النظام بدون اتصال بالإنترنت بشكل كامل.

### هل يدعم لغات أخرى غير العربية؟

حالياً النظام يدعم العربية فقط، لكن يمكن إضافة لغات أخرى بسهولة. راجع [CONTRIBUTING.md](CONTRIBUTING.md) للمساهمة.

### هل يمكن استخدامه خارج السعودية؟

نعم، يمكن استخدامه في أي مكان، لكنه مُحسّن للسعودية ويحتوي على قاعدة بيانات للمدن السعودية.

---

## 🔧 التثبيت والإعداد

### ما هي المتطلبات الأساسية؟

- Node.js 14 أو أحدث
- متصفح حديث (Chrome, Firefox, Safari, Edge)
- 100 MB مساحة تخزين على الأقل

### كيف أثبت النظام؟

```bash
# Windows
setup.bat

# Linux/Mac
chmod +x setup.sh
./setup.sh
```

أو يدوياً:
```bash
npm install
npm start
```

### لماذا يفشل التثبيت؟

**الأسباب الشائعة:**

1. **Node.js غير مثبت**
   ```bash
   # تحقق من التثبيت
   node --version
   npm --version
   ```

2. **إصدار Node.js قديم**
   - قم بتحديث Node.js إلى الإصدار 14 أو أحدث

3. **مشاكل في الشبكة**
   ```bash
   # استخدم مرآة npm مختلفة
   npm config set registry https://registry.npmjs.org/
   ```

4. **صلاحيات غير كافية**
   ```bash
   # Linux/Mac - استخدم sudo
   sudo npm install
   ```

### كيف أحدث النظام؟

```bash
# سحب آخر التحديثات (إذا كنت تستخدم Git)
git pull

# تثبيت المكتبات الجديدة
npm install

# إعادة تشغيل الخادم
npm start
```

---

## 💻 الاستخدام

### كيف أضيف موقعاً جديداً؟

1. اضغط زر ➕ "إضافة موقع"
2. أدخل الاسم والإحداثيات
3. أو اضغط "تحديد من الخريطة"
4. اضغط "حفظ"

### كيف أحذف موقعاً؟

1. افتح تبويب "المواقع المحفوظة"
2. اضغط زر 🗑️ بجانب الموقع
3. أكد الحذف

### كيف أصدّر مواقعي؟

1. اضغط زر ⚙️ "الإعدادات"
2. اختر "تصدير المواقع"
3. سيتم تحميل ملف JSON

### كيف أستورد مواقع محفوظة؟

1. اضغط زر ⚙️ "الإعدادات"
2. اختر "استيراد المواقع"
3. اختر ملف JSON
4. اضغط "استيراد"

### كيف أبحث عن مدينة؟

1. اضغط زر 🔍 "البحث"
2. أدخل اسم المدينة
3. اختر من النتائج
4. سيتم الانتقال للموقع على الخريطة

### كيف أبدأ التوجيه؟

1. افتح تبويب "التوجيه" 🧭
2. اختر الوجهة من القائمة
3. اضغط "ابدأ التوجيه"
4. سيظهر المسار مع المسافة والوقت

### كيف أتتبع موقعي؟

1. افتح تبويب "التتبع" 📱
2. اضغط "بدء التتبع"
3. امنح المتصفح إذن الوصول للموقع
4. راقب موقعك وسرعتك

---

## 🗺️ الخرائط

### من أين تأتي بيانات الخرائط؟

من OpenStreetMap (OSM)، وهي خرائط مفتوحة المصدر ومجانية.

### هل يمكن استخدام خرائط Google؟

لا، خرائط Google تتطلب اتصال بالإنترنت ومفتاح API مدفوع. نستخدم OSM لأنها مجانية وتدعم الأوفلاين.

### كيف أعد الخرائط للعمل أوفلاين؟

راجع الدليل الشامل: [OFFLINE_MAPS_SETUP.md](OFFLINE_MAPS_SETUP.md)

**الطرق المتاحة:**
1. TileServer GL (موصى به)
2. Leaflet.Offline
3. MapTiler Server
4. تحميل Tiles يدوياً

### هل يمكن تحميل خرائط مناطق محددة فقط؟

نعم، يمكنك تحميل خرائط منطقة معينة (مثل الرياض فقط) لتوفير المساحة. راجع [OFFLINE_MAPS_SETUP.md](OFFLINE_MAPS_SETUP.md).

### ما حجم الخرائط الأوفلاين؟

- **السعودية كاملة**: ~2-5 GB
- **منطقة واحدة**: ~200-500 MB
- **مدينة واحدة**: ~50-100 MB

---

## 🔧 المشاكل التقنية

### الخريطة لا تظهر

**الحلول:**

1. **تحقق من تشغيل الخادم**
   ```bash
   npm start
   ```

2. **تحقق من عنوان URL**
   - يجب أن يكون: `http://localhost:3000`

3. **افتح Console في المتصفح**
   - اضغط F12
   - ابحث عن أخطاء في Console

4. **امسح الذاكرة المؤقتة**
   - Ctrl + Shift + Delete
   - امسح Cache

### المتصفح لا يطلب إذن الموقع

**الحلول:**

1. **استخدم localhost أو https**
   - Geolocation API يعمل فقط على localhost أو https

2. **تحقق من إعدادات المتصفح**
   - Chrome: Settings → Privacy → Site Settings → Location
   - Firefox: Settings → Privacy → Permissions → Location

3. **أعد تحميل الصفحة**
   - Ctrl + Shift + R

### خطأ "Port 3000 is already in use"

**الحلول:**

```bash
# Windows
netstat -ano | findstr :3000
taskkill /PID [PID] /F

# Linux/Mac
lsof -i :3000
kill -9 [PID]
```

أو غيّر المنفذ في `server.js`:
```javascript
const PORT = process.env.PORT || 8080;
```

### قاعدة البيانات لا تعمل

**الحلول:**

1. **احذف قاعدة البيانات وأعد إنشاءها**
   ```bash
   rm daleel.db
   npm start
   ```

2. **تحقق من الصلاحيات**
   ```bash
   # Linux/Mac
   chmod 666 daleel.db
   ```

### التوجيه لا يعمل

**الأسباب:**

1. **لا يوجد اتصال بالإنترنت**
   - التوجيه يحتاج إنترنت حالياً
   - راجع [OFFLINE_MAPS_SETUP.md](OFFLINE_MAPS_SETUP.md) لإعداد OSRM

2. **الموقع الحالي غير محدد**
   - فعّل GPS أولاً

### البحث لا يعطي نتائج

**الحلول:**

1. **تحقق من الإملاء**
   - جرب كتابة جزء من الاسم

2. **البحث يدعم العربية فقط حالياً**
   - استخدم الأحرف العربية

---

## 🔒 الأمان والخصوصية

### هل بياناتي آمنة؟

نعم، جميع البيانات محفوظة محلياً على جهازك في قاعدة بيانات SQLite. لا يتم إرسال أي بيانات لخوادم خارجية.

### هل يتم تتبع موقعي؟

لا، التتبع يتم محلياً فقط. البيانات لا تغادر جهازك أبداً.

### كيف أحمي بياناتي؟

1. **نسخ احتياطي منتظم**
   ```bash
   cp daleel.db daleel.db.backup
   ```

2. **تصدير المواقع**
   - صدّر مواقعك بانتظام

3. **تشفير القرص**
   - استخدم BitLocker (Windows) أو FileVault (Mac)

### هل يمكن إضافة كلمة مرور؟

نعم، يمكن إضافة مصادقة أساسية. راجع [DEPLOYMENT.md](DEPLOYMENT.md) قسم الأمان.

---

## 👨‍💻 التطوير

### كيف أساهم في المشروع؟

راجع [CONTRIBUTING.md](CONTRIBUTING.md) للتفاصيل الكاملة.

**الخطوات الأساسية:**
1. Fork المشروع
2. أنشئ فرعاً جديداً
3. قم بالتعديلات
4. أرسل Pull Request

### كيف أضيف ميزة جديدة؟

1. **افتح Issue أولاً**
   - اشرح الميزة المقترحة
   - انتظر الموافقة

2. **طور الميزة**
   - اتبع معايير الكود
   - أضف تعليقات

3. **اختبر الميزة**
   - تأكد من عملها بشكل صحيح

4. **أرسل Pull Request**
   - اشرح التغييرات بالتفصيل

### كيف أبلغ عن خطأ؟

افتح Issue جديد على GitHub مع:
- وصف المشكلة
- خطوات إعادة الإنتاج
- لقطات شاشة (إن وجدت)
- معلومات البيئة (نظام التشغيل، المتصفح، إلخ)

### أين الكود المصدري؟

```
DLYL/
├── server.js          # الخادم
├── public/
│   ├── index.html    # الواجهة
│   ├── css/style.css # التصميم
│   └── js/
│       ├── app.js    # المنطق الرئيسي
│       └── saudi-cities.js  # قاعدة بيانات المدن
```

### كيف أضيف مدناً جديدة؟

عدّل ملف `public/js/saudi-cities.js`:

```javascript
const saudiCities = [
    // ... المدن الموجودة
    {
        name: "اسم المدينة",
        lat: 24.7136,
        lng: 46.6753,
        region: "المنطقة"
    }
];
```

---

## 🌐 الشبكات

### كيف أستخدم النظام على الشبكة المحلية؟

1. **احصل على IP الخاص بك**
   ```bash
   # Windows
   ipconfig
   
   # Linux/Mac
   ifconfig
   ```

2. **افتح على الأجهزة الأخرى**
   ```
   http://[YOUR-IP]:3000
   ```

### كيف أنشر النظام على خادم؟

راجع [DEPLOYMENT.md](DEPLOYMENT.md) للتفاصيل الكاملة.

**الخيارات:**
- VPS (DigitalOcean, AWS, etc.)
- Docker
- Nginx Reverse Proxy
- PM2 Process Manager

---

## 📱 الجوال

### هل يعمل على الجوال؟

نعم، الواجهة متجاوبة وتعمل على جميع الأجهزة.

### هل يوجد تطبيق جوال؟

حالياً لا، لكن يمكن استخدام النظام كـ PWA (Progressive Web App) في المستقبل.

### كيف أثبته على الشاشة الرئيسية؟

**Chrome (Android):**
1. افتح الموقع
2. Menu → Add to Home Screen

**Safari (iOS):**
1. افتح الموقع
2. Share → Add to Home Screen

---

## 💡 نصائح وحيل

### كيف أحسّن الأداء؟

1. **استخدم خرائط أوفلاين**
2. **قلل عدد المواقع المحفوظة**
3. **امسح السجل القديم**
4. **استخدم متصفح حديث**

### كيف أوفر المساحة؟

1. **حمّل خرائط مناطق محددة فقط**
2. **احذف السجل القديم**
3. **صدّر واحذف المواقع غير المستخدمة**

### كيف أستخدم النظام في السيارة؟

1. **استخدم جهاز لوحي أو جوال**
2. **فعّل وضع ملء الشاشة**
3. **ثبت الجهاز بشكل آمن**
4. **فعّل التتبع قبل البدء**

---

## 📞 الدعم

### أين أجد المساعدة؟

- 📖 [README.md](README.md) - نظرة عامة
- 📘 [USAGE_GUIDE.md](USAGE_GUIDE.md) - دليل الاستخدام
- 🚀 [QUICK_START.md](QUICK_START.md) - البدء السريع
- 🐛 GitHub Issues - للمشاكل
- 💬 GitHub Discussions - للنقاشات

### كيف أتواصل مع المطورين؟

- افتح Issue على GitHub
- شارك في Discussions
- أرسل Pull Request

---

## 🎓 موارد إضافية

### دروس فيديو

قريباً...

### مقالات

قريباً...

### أمثلة

- [sample-data.json](sample-data.json) - 20 موقعاً تجريبياً

---

**لم تجد إجابة لسؤالك؟**

افتح Issue جديد على GitHub وسنساعدك! 🚀

