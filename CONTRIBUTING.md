# 🤝 دليل المساهمة في مشروع دليل

شكراً لاهتمامك بالمساهمة في مشروع "دليل"! نرحب بجميع المساهمات سواء كانت إصلاح أخطاء، إضافة ميزات جديدة، تحسين التوثيق، أو حتى اقتراحات.

---

## 📋 جدول المحتويات

- [كيفية المساهمة](#كيفية-المساهمة)
- [معايير الكود](#معايير-الكود)
- [عملية المراجعة](#عملية-المراجعة)
- [الإبلاغ عن المشاكل](#الإبلاغ-عن-المشاكل)
- [اقتراح ميزات جديدة](#اقتراح-ميزات-جديدة)

---

## 🚀 كيفية المساهمة

### 1. Fork المشروع

انقر على زر "Fork" في أعلى الصفحة لإنشاء نسخة من المشروع في حسابك.

### 2. استنساخ المشروع

```bash
git clone https://github.com/YOUR-USERNAME/daleel.git
cd daleel
```

### 3. إنشاء فرع جديد

```bash
# للميزات الجديدة
git checkout -b feature/amazing-feature

# لإصلاح الأخطاء
git checkout -b fix/bug-description

# للتوثيق
git checkout -b docs/documentation-update
```

### 4. إجراء التغييرات

قم بإجراء التغييرات المطلوبة في الكود.

### 5. اختبار التغييرات

```bash
# تثبيت المكتبات
npm install

# تشغيل التطبيق
npm start

# اختبر التغييرات في المتصفح
```

### 6. Commit التغييرات

```bash
git add .
git commit -m "وصف واضح للتغييرات"
```

#### نمط رسائل Commit

استخدم رسائل واضحة ومفصلة:

```
نوع: وصف مختصر

وصف تفصيلي للتغييرات (اختياري)

Fixes #123 (إذا كان يحل مشكلة معينة)
```

أنواع Commit:
- `feat:` - ميزة جديدة
- `fix:` - إصلاح خطأ
- `docs:` - تحديث التوثيق
- `style:` - تنسيق الكود (لا يؤثر على الوظيفة)
- `refactor:` - إعادة هيكلة الكود
- `test:` - إضافة اختبارات
- `chore:` - مهام صيانة

أمثلة:
```
feat: إضافة ميزة البحث الصوتي
fix: إصلاح مشكلة عرض الخريطة على الجوال
docs: تحديث دليل الاستخدام
```

### 7. Push للفرع

```bash
git push origin feature/amazing-feature
```

### 8. فتح Pull Request

1. اذهب إلى صفحة المشروع الأصلي
2. انقر على "New Pull Request"
3. اختر فرعك
4. أضف وصفاً تفصيلياً للتغييرات
5. انقر "Create Pull Request"

---

## 📝 معايير الكود

### JavaScript

#### 1. استخدام ES6+

```javascript
// ✅ جيد
const locations = [];
let currentLocation = null;
const getLocation = () => { ... };

// ❌ تجنب
var locations = [];
var currentLocation = null;
function getLocation() { ... }
```

#### 2. التسمية

```javascript
// ✅ جيد - أسماء واضحة ومعبرة
const savedLocations = [];
const currentUserPosition = null;
function calculateDistance(lat1, lng1, lat2, lng2) { ... }

// ❌ تجنب - أسماء غير واضحة
const arr = [];
const pos = null;
function calc(a, b, c, d) { ... }
```

#### 3. التعليقات

```javascript
// ✅ جيد - تعليقات بالعربية للوضوح
// دالة لحساب المسافة بين نقطتين باستخدام Haversine formula
function calculateDistance(lat1, lng1, lat2, lng2) {
    // نصف قطر الأرض بالكيلومتر
    const R = 6371;
    // ... باقي الكود
}

// ❌ تجنب - بدون تعليقات أو تعليقات غير واضحة
function calc(a, b, c, d) {
    const x = 6371;
    // ...
}
```

#### 4. معالجة الأخطاء

```javascript
// ✅ جيد
async function loadLocations() {
    try {
        const response = await fetch('/api/locations');
        const result = await response.json();
        
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('Error loading locations:', error);
        showNotification('خطأ في تحميل المواقع', 'error');
        return [];
    }
}

// ❌ تجنب
async function loadLocations() {
    const response = await fetch('/api/locations');
    const result = await response.json();
    return result.data;
}
```

### CSS

#### 1. استخدام المتغيرات

```css
/* ✅ جيد */
:root {
    --primary-color: #1a5f3f;
    --text-color: #333;
}

.button {
    background: var(--primary-color);
    color: var(--text-color);
}

/* ❌ تجنب */
.button {
    background: #1a5f3f;
    color: #333;
}
```

#### 2. التنظيم

```css
/* ✅ جيد - منظم ومرتب */
.location-item {
    /* Layout */
    display: flex;
    padding: 1rem;
    
    /* Visual */
    background: var(--secondary-color);
    border-radius: 10px;
    
    /* Typography */
    font-size: 1rem;
    
    /* Animation */
    transition: all 0.3s;
}
```

### HTML

#### 1. الدلالات الصحيحة

```html
<!-- ✅ جيد -->
<header class="header">
    <nav class="navigation">
        <button class="nav-btn">المواقع</button>
    </nav>
</header>

<!-- ❌ تجنب -->
<div class="header">
    <div class="navigation">
        <div class="nav-btn">المواقع</div>
    </div>
</div>
```

#### 2. إمكانية الوصول

```html
<!-- ✅ جيد -->
<button aria-label="إضافة موقع جديد" title="إضافة موقع">
    <i class="fas fa-plus"></i>
</button>

<!-- ❌ تجنب -->
<button>
    <i class="fas fa-plus"></i>
</button>
```

---

## 🔍 عملية المراجعة

### ما نبحث عنه

1. **الوظيفة**: هل التغييرات تعمل كما هو متوقع؟
2. **الجودة**: هل الكود نظيف ومنظم؟
3. **التوثيق**: هل التغييرات موثقة بشكل جيد؟
4. **الاختبار**: هل تم اختبار التغييرات؟
5. **التوافق**: هل التغييرات متوافقة مع الكود الموجود؟

### مدة المراجعة

- عادةً نراجع Pull Requests خلال 2-3 أيام
- قد تستغرق التغييرات الكبيرة وقتاً أطول

### التعليقات والتعديلات

- قد نطلب تعديلات على الكود
- الرجاء الرد على التعليقات في أقرب وقت
- لا تتردد في طرح الأسئلة

---

## 🐛 الإبلاغ عن المشاكل

### قبل الإبلاغ

1. تحقق من أن المشكلة لم يتم الإبلاغ عنها مسبقاً
2. تأكد من استخدام أحدث إصدار
3. حاول إعادة إنتاج المشكلة

### كيفية الإبلاغ

افتح Issue جديد وقدم المعلومات التالية:

```markdown
## وصف المشكلة
وصف واضح ومختصر للمشكلة

## خطوات إعادة الإنتاج
1. اذهب إلى '...'
2. انقر على '...'
3. شاهد الخطأ

## السلوك المتوقع
ما كان يجب أن يحدث

## السلوك الفعلي
ما حدث بالفعل

## لقطات الشاشة
إن وجدت

## البيئة
- نظام التشغيل: [مثل Windows 10]
- المتصفح: [مثل Chrome 90]
- إصدار Node.js: [مثل 18.0.0]

## معلومات إضافية
أي معلومات أخرى مفيدة
```

---

## 💡 اقتراح ميزات جديدة

### قبل الاقتراح

1. تحقق من أن الميزة لم يتم اقتراحها مسبقاً
2. تأكد من أن الميزة تتناسب مع أهداف المشروع

### كيفية الاقتراح

افتح Issue جديد بعنوان يبدأ بـ `[Feature Request]`:

```markdown
## وصف الميزة
وصف واضح للميزة المقترحة

## المشكلة التي تحلها
ما المشكلة التي ستحلها هذه الميزة؟

## الحل المقترح
كيف تتصور تنفيذ هذه الميزة؟

## البدائل
هل فكرت في حلول بديلة؟

## معلومات إضافية
أي معلومات أخرى، رسومات، أمثلة، إلخ
```

---

## 📚 مجالات المساهمة

### 1. الكود

- إضافة ميزات جديدة
- إصلاح الأخطاء
- تحسين الأداء
- إعادة هيكلة الكود

### 2. التوثيق

- تحسين README
- إضافة أمثلة
- ترجمة التوثيق
- إنشاء دروس فيديو

### 3. التصميم

- تحسين واجهة المستخدم
- إضافة رسوم متحركة
- تحسين تجربة المستخدم
- إنشاء أيقونات

### 4. الاختبار

- كتابة اختبارات
- اختبار الميزات الجديدة
- الإبلاغ عن الأخطاء

### 5. الترجمة

- ترجمة الواجهة للغات أخرى
- تحسين الترجمة العربية

---

## 🎯 أولويات المشروع

### أولوية عالية
- إصلاح الأخطاء الحرجة
- تحسينات الأمان
- تحسينات الأداء

### أولوية متوسطة
- ميزات جديدة مطلوبة
- تحسينات واجهة المستخدم
- تحديث التوثيق

### أولوية منخفضة
- ميزات تجريبية
- تحسينات تجميلية

---

## 📞 التواصل

- **GitHub Issues**: للمشاكل والاقتراحات
- **Pull Requests**: للمساهمات في الكود
- **Discussions**: للنقاشات العامة

---

## 🙏 شكراً

شكراً لمساهمتك في جعل "دليل" أفضل! كل مساهمة، مهما كانت صغيرة، تُحدث فرقاً.

---

## 📜 ميثاق السلوك

نتوقع من جميع المساهمين:

- الاحترام والتقدير للآخرين
- قبول النقد البناء
- التركيز على ما هو أفضل للمجتمع
- إظهار التعاطف تجاه أعضاء المجتمع الآخرين

نحن ملتزمون بتوفير بيئة ترحيبية وشاملة للجميع.

