# 🚀 دليل البدء السريع - نظام دليل

## ⚡ ابدأ في 3 خطوات

### الخطوة 1️⃣: التثبيت

#### Windows:
```bash
setup.bat
```

#### Linux/Mac:
```bash
chmod +x setup.sh
./setup.sh
```

### الخطوة 2️⃣: التشغيل

```bash
npm start
```

### الخطوة 3️⃣: الوصول

افتح المتصفح على: **http://localhost:3000**

---

## 🎯 البدء الفوري

### 1. تحديد موقعك الحالي

1. اضغط على زر 🎯 في الخريطة
2. امنح المتصفح إذن الوصول للموقع
3. سيتم عرض موقعك على الخريطة

### 2. إضافة موقع مفضل

1. اضغط على زر ➕ "إضافة موقع"
2. أدخل اسم الموقع (مثل: المنزل)
3. اختر إحدى الطرق:
   - أدخل الإحداثيات يدوياً
   - اضغط "تحديد من الخريطة" واختر الموقع
4. أضف وصفاً (اختياري)
5. اضغط "حفظ"

### 3. التوجيه إلى موقع

1. افتح تبويب "التوجيه" 🧭
2. اختر الوجهة من القائمة المنسدلة
3. اضغط "ابدأ التوجيه"
4. سيظهر المسار على الخريطة مع المسافة والوقت

### 4. تتبع موقعك

1. افتح تبويب "التتبع" 📱
2. اضغط "بدء التتبع"
3. راقب موقعك وسرعتك في الوقت الفعلي
4. اضغط "إيقاف التتبع" عند الانتهاء

### 5. البحث عن موقع

1. اضغط على زر 🔍 "البحث"
2. أدخل اسم المدينة أو الموقع
3. اختر من النتائج
4. سيتم الانتقال للموقع على الخريطة

---

## 📱 الاستخدام على الشبكة المحلية

### 1. احصل على عنوان IP

#### Windows:
```cmd
ipconfig
```
ابحث عن `IPv4 Address`

#### Linux/Mac:
```bash
ifconfig
```

### 2. افتح على الأجهزة الأخرى

```
http://[YOUR-IP]:3000
```

مثال: `http://*************:3000`

---

## 💾 استيراد البيانات التجريبية

### الطريقة 1: من الواجهة

1. اضغط على زر ⚙️ "الإعدادات"
2. اختر "استيراد المواقع"
3. اختر ملف `sample-data.json`
4. اضغط "استيراد"

### الطريقة 2: من API

```bash
curl -X POST http://localhost:3000/api/import/locations \
  -H "Content-Type: application/json" \
  -d @sample-data.json
```

---

## 🔧 الأوامر الأساسية

### تشغيل الخادم
```bash
npm start
```

### إيقاف الخادم
اضغط `Ctrl + C` في Terminal

### إعادة تشغيل الخادم
```bash
# أوقف الخادم أولاً (Ctrl + C)
npm start
```

### تثبيت المكتبات من جديد
```bash
npm install
```

---

## 🗺️ إعداد الخرائط الأوفلاين (اختياري)

للعمل بدون إنترنت بشكل كامل، راجع:
📖 **[OFFLINE_MAPS_SETUP.md](OFFLINE_MAPS_SETUP.md)**

---

## 🆘 حل المشاكل السريع

### الخريطة لا تظهر
```bash
# تأكد من تشغيل الخادم
npm start

# افتح http://localhost:3000
```

### المتصفح لا يطلب إذن الموقع
- استخدم `localhost` أو `https`
- تحقق من إعدادات المتصفح

### خطأ في المنفذ 3000
```bash
# المنفذ مستخدم، أوقف العملية
# Windows
netstat -ano | findstr :3000
taskkill /PID [PID] /F

# Linux/Mac
lsof -i :3000
kill -9 [PID]
```

### قاعدة البيانات لا تعمل
```bash
# احذف قاعدة البيانات وأعد التشغيل
rm daleel.db
npm start
```

---

## 📚 التوثيق الكامل

| الملف | الوصف |
|-------|--------|
| [README.md](README.md) | دليل المشروع الشامل |
| [USAGE_GUIDE.md](USAGE_GUIDE.md) | دليل الاستخدام التفصيلي |
| [OFFLINE_MAPS_SETUP.md](OFFLINE_MAPS_SETUP.md) | إعداد الخرائط الأوفلاين |
| [ARCHITECTURE.md](ARCHITECTURE.md) | البنية التقنية |
| [DEPLOYMENT.md](DEPLOYMENT.md) | دليل النشر |
| [CONTRIBUTING.md](CONTRIBUTING.md) | دليل المساهمة |

---

## 🎓 أمثلة سريعة

### مثال 1: حفظ موقع المنزل

1. اضغط "إضافة موقع"
2. الاسم: `المنزل`
3. اضغط "تحديد من الخريطة"
4. اختر موقع منزلك
5. اضغط "حفظ"

### مثال 2: التوجيه للعمل

1. احفظ موقع العمل أولاً
2. افتح تبويب "التوجيه"
3. اختر "العمل" من القائمة
4. اضغط "ابدأ التوجيه"

### مثال 3: تتبع رحلة

1. افتح تبويب "التتبع"
2. اضغط "بدء التتبع"
3. ابدأ رحلتك
4. راقب السرعة والموقع
5. اضغط "إيقاف التتبع" عند الوصول

---

## 🔑 اختصارات لوحة المفاتيح

| الاختصار | الوظيفة |
|----------|---------|
| `Ctrl + F` | فتح البحث |
| `Esc` | إغلاق النوافذ المنبثقة |
| `+` | تكبير الخريطة |
| `-` | تصغير الخريطة |

---

## 💡 نصائح سريعة

### 1. حفظ المواقع المهمة
احفظ المواقع التي تزورها كثيراً (المنزل، العمل، المسجد، إلخ)

### 2. استخدام الأوصاف
أضف أوصافاً للمواقع لتسهيل التعرف عليها

### 3. النسخ الاحتياطي
صدّر مواقعك بانتظام للحفاظ على نسخة احتياطية

### 4. البحث السريع
استخدم البحث للوصول السريع للمواقع والمدن

### 5. تتبع الرحلات
فعّل التتبع قبل بدء الرحلة لحفظ سجل كامل

---

## 📊 الميزات الرئيسية

- ✅ خريطة تفاعلية للسعودية
- ✅ حفظ مواقع غير محدودة
- ✅ توجيه ذكي مع حساب المسافة والوقت
- ✅ تتبع GPS في الوقت الفعلي
- ✅ بحث في 150+ مدينة سعودية
- ✅ تصدير واستيراد البيانات
- ✅ واجهة عربية أنيقة
- ✅ يعمل أوفلاين (بعد الإعداد)

---

## 🌐 المتصفحات المدعومة

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

---

## 📞 الدعم

### للمساعدة
- 📖 راجع [USAGE_GUIDE.md](USAGE_GUIDE.md)
- 🐛 افتح Issue على GitHub
- 💬 شارك في Discussions

### للمساهمة
- 🤝 راجع [CONTRIBUTING.md](CONTRIBUTING.md)
- 🔧 Fork المشروع
- 📝 أرسل Pull Request

---

## 🎉 ابدأ الآن!

```bash
# 1. ثبّت المكتبات
npm install

# 2. شغّل الخادم
npm start

# 3. افتح المتصفح
# http://localhost:3000
```

**استمتع باستخدام دليل! 🚀**

---

<div align="center">

**صُنع بـ ❤️ للمملكة العربية السعودية**

[README](README.md) • [دليل الاستخدام](USAGE_GUIDE.md) • [النشر](DEPLOYMENT.md)

</div>

