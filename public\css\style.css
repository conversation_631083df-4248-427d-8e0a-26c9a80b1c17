/* ========== Variables ========== */
:root {
    --primary-color: #1a5f3f;
    --primary-dark: #0f3d28;
    --primary-light: #2d8659;
    --secondary-color: #f0f4f1;
    --danger-color: #dc3545;
    --text-color: #333;
    --text-light: #666;
    --border-color: #ddd;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* ========== Reset & Base ========== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: var(--text-color);
    overflow: hidden;
}

/* ========== Header ========== */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
    z-index: 1000;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 1.8rem;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* ========== Container ========== */
.container {
    display: flex;
    height: calc(100vh - 70px);
    position: relative;
}

/* ========== Sidebar ========== */
.sidebar {
    width: 380px;
    background: white;
    box-shadow: var(--shadow-lg);
    display: flex;
    flex-direction: column;
    z-index: 100;
    transition: transform 0.3s;
}

.sidebar.hidden {
    transform: translateX(100%);
}

.sidebar-header {
    padding: 1.25rem;
    border-bottom: 2px solid var(--secondary-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h2 {
    font-size: 1.25rem;
    color: var(--primary-color);
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    transition: color 0.3s;
}

.btn-close:hover {
    color: var(--danger-color);
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

/* ========== Navigation Buttons ========== */
.nav-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.nav-btn {
    background: var(--secondary-color);
    border: 2px solid transparent;
    padding: 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-color);
}

.nav-btn i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.nav-btn:hover {
    background: var(--primary-light);
    color: white;
    transform: translateY(-2px);
}

.nav-btn:hover i {
    color: white;
}

.nav-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-dark);
}

.nav-btn.active i {
    color: white;
}

/* ========== Tab Content ========== */
.tab-content {
    margin-top: 1rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.tab-header h3 {
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* ========== Buttons ========== */
.btn-primary, .btn-secondary, .btn-danger {
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-color);
}

.btn-secondary:hover {
    background: #e0e8e3;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn-block {
    width: 100%;
    margin-bottom: 0.75rem;
}

/* ========== Locations List ========== */
.locations-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.location-item {
    background: var(--secondary-color);
    padding: 1rem;
    border-radius: 10px;
    border-right: 4px solid var(--primary-color);
    transition: all 0.3s;
}

.location-item:hover {
    background: #e0e8e3;
    transform: translateX(-5px);
}

.location-header {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-bottom: 0.5rem;
}

.location-name {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.location-actions {
    display: flex;
    gap: 0.5rem;
}

.location-actions button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
    font-size: 1rem;
    transition: color 0.3s;
}

.location-actions button:hover {
    color: var(--primary-color);
}

.location-actions .delete-btn:hover {
    color: var(--danger-color);
}

.location-coords {
    font-size: 0.85rem;
    color: var(--text-light);
    display: flex;
    gap: 1rem;
}

.location-description {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color);
}

.empty-state {
    text-align: center;
    color: var(--text-light);
    padding: 2rem;
    font-style: italic;
}

/* ========== Forms ========== */
.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
    font-family: inherit;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1.5rem;
}

/* ========== Route Info ========== */
.route-info {
    background: var(--secondary-color);
    padding: 1.25rem;
    border-radius: 10px;
    margin-top: 1.5rem;
}

.route-info h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

/* ========== Map Container ========== */
.map-container {
    flex: 1;
    position: relative;
}

#map {
    width: 100%;
    height: 100%;
}

/* ========== Floating Action Button ========== */
.fab {
    position: absolute;
    bottom: 2rem;
    left: 2rem;
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s;
    z-index: 50;
}

.fab:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}



/* ========== Modal ========== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 2px solid var(--secondary-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--primary-color);
}

.modal-body {
    padding: 1.5rem;
}

/* ========== Leaflet Map Controls ========== */
.leaflet-control-zoom {
    border: none !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
}

.leaflet-control-zoom a {
    background-color: white !important;
    border: 1px solid #ccc !important;
    color: #333 !important;
    font-size: 18px !important;
    font-weight: bold !important;
    line-height: 30px !important;
    width: 32px !important;
    height: 32px !important;
    text-align: center !important;
    text-decoration: none !important;
    display: block !important;
    margin: 0 !important;
    border-radius: 0 !important;
}

.leaflet-control-zoom a:first-child {
    border-top-left-radius: 4px !important;
    border-top-right-radius: 4px !important;
    border-bottom: none !important;
}

.leaflet-control-zoom a:last-child {
    border-bottom-left-radius: 4px !important;
    border-bottom-right-radius: 4px !important;
    border-top: none !important;
}

.leaflet-control-zoom a:hover {
    background-color: #f4f4f4 !important;
    color: #000 !important;
}

.leaflet-control-zoom a:focus {
    outline: 2px solid var(--primary-color) !important;
    outline-offset: 2px !important;
}

/* Custom Location Control */
.leaflet-control-locate {
    border: none !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
    margin-top: 10px !important;
}

.leaflet-control-locate a {
    background-color: white !important;
    border: 1px solid #ccc !important;
    color: #333 !important;
    font-size: 16px !important;
    line-height: 30px !important;
    width: 32px !important;
    height: 32px !important;
    text-align: center !important;
    text-decoration: none !important;
    display: block !important;
    border-radius: 4px !important;
}

.leaflet-control-locate a:hover {
    background-color: #f4f4f4 !important;
    color: #000 !important;
}

.leaflet-control-locate a:focus {
    outline: 2px solid var(--primary-color) !important;
    outline-offset: 2px !important;
}

/* Fix for overlapping controls */
.leaflet-top.leaflet-left {
    top: 10px !important;
    left: 10px !important;
}

.leaflet-control {
    margin: 0 !important;
    margin-bottom: 10px !important;
    clear: both !important;
}

/* ========== Responsive ========== */
@media (max-width: 768px) {
    .sidebar {
        position: absolute;
        right: 0;
        height: 100%;
        transform: translateX(100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .nav-buttons {
        grid-template-columns: 1fr;
    }

    /* Mobile map controls */
    .leaflet-control-zoom a,
    .leaflet-control-locate a {
        width: 40px !important;
        height: 40px !important;
        line-height: 38px !important;
        font-size: 20px !important;
    }

    .leaflet-top.leaflet-left {
        top: 15px !important;
        left: 15px !important;
    }
}

