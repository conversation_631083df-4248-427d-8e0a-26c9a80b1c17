# 🧭 Bousla System Requirements & Installation Guide

## 📋 System Requirements

### Minimum Requirements

| Component | Requirement | Recommended |
|-----------|-------------|-------------|
| **Operating System** | Windows 10, macOS 10.14, Ubuntu 18.04 | Latest versions |
| **Node.js** | 14.0.0 or higher | 18.0.0 or higher |
| **RAM** | 2 GB | 4 GB or more |
| **Storage** | 500 MB free space | 2 GB for offline maps |
| **Browser** | Chrome 90+, Firefox 88+, Safari 14+, Edge 90+ | Latest versions |
| **Internet** | Required for initial setup | Optional after setup |

### Hardware Requirements

- **CPU**: Any modern processor (Intel/AMD/ARM)
- **Network**: Ethernet or Wi-Fi for network access
- **GPS**: Optional (for location tracking on mobile devices)

---

## 🔧 Prerequisites Installation

### 1. Install Node.js

#### Windows:
1. Go to [https://nodejs.org/](https://nodejs.org/)
2. Download the **LTS version** (recommended)
3. Run the installer (.msi file)
4. Follow the installation wizard
5. Restart your computer

#### macOS:
```bash
# Option 1: Download from website
# Go to https://nodejs.org/ and download the LTS version

# Option 2: Using Homebrew (if installed)
brew install node

# Option 3: Using MacPorts (if installed)
sudo port install nodejs18
```

#### Linux (Ubuntu/Debian):
```bash
# Update package index
sudo apt update

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

#### Linux (CentOS/RHEL/Fedora):
```bash
# Install Node.js
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# For Fedora, use dnf instead of yum
sudo dnf install -y nodejs
```

### 2. Verify Installation

Open terminal/command prompt and run:
```bash
node --version
npm --version
```

You should see version numbers like:
```
v18.17.0
9.6.7
```

---

## 📦 Project Installation

### Method 1: Automatic Setup (Recommended)

#### Windows:
1. Download the project files
2. Extract to a folder (e.g., `C:\Daleel`)
3. Open Command Prompt as Administrator
4. Navigate to the project folder:
   ```cmd
   cd C:\Daleel
   ```
5. Run the setup script:
   ```cmd
   setup.bat
   ```

#### Linux/macOS:
1. Download the project files
2. Extract to a folder (e.g., `/home/<USER>/Daleel`)
3. Open Terminal
4. Navigate to the project folder:
   ```bash
   cd /home/<USER>/Daleel
   ```
5. Make the script executable:
   ```bash
   chmod +x setup.sh
   ```
6. Run the setup script:
   ```bash
   ./setup.sh
   ```

### Method 2: Manual Installation

1. **Download/Extract Project**
   ```bash
   # If using Git
   git clone <repository-url>
   cd daleel
   
   # Or extract downloaded ZIP file
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Verify Files**
   Make sure these files exist:
   - `server.js`
   - `package.json`
   - `public/index.html`
   - `public/css/style.css`
   - `public/js/app.js`
   - `public/js/saudi-cities.js`

---

## 🚀 Running the Application

### Start the Server

```bash
npm start
```

You should see:
```
🚀 Daleel server running on http://localhost:3000
📍 Offline navigation system ready
```

### Access the Application

1. **Local Access:**
   Open your browser and go to: `http://localhost:3000`

2. **Network Access:**
   - Find your IP address:
     - **Windows**: `ipconfig`
     - **Linux/macOS**: `ifconfig` or `ip addr`
   - On other devices, go to: `http://YOUR-IP:3000`
   - Example: `http://*************:3000`

---

## 🌐 Network Configuration

### For Local Network Access

#### Windows Firewall:
```powershell
# Run PowerShell as Administrator
New-NetFirewallRule -DisplayName "Daleel Navigation" -Direction Inbound -LocalPort 3000 -Protocol TCP -Action Allow
```

#### Linux Firewall (UFW):
```bash
sudo ufw allow 3000/tcp
```

#### Linux Firewall (iptables):
```bash
sudo iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
```

#### macOS Firewall:
1. Go to System Preferences → Security & Privacy → Firewall
2. Click "Firewall Options"
3. Add Node.js to allowed applications

---

## 📱 Browser Configuration

### Enable Location Services

#### Chrome:
1. Go to Settings → Privacy and security → Site Settings
2. Click "Location"
3. Make sure "Ask before accessing" is enabled
4. Add `http://localhost:3000` to allowed sites

#### Firefox:
1. Go to Settings → Privacy & Security
2. Scroll to "Permissions" section
3. Click "Settings" next to Location
4. Add `http://localhost:3000` to allowed sites

#### Safari:
1. Go to Safari → Preferences → Websites
2. Click "Location" in the left sidebar
3. Set localhost to "Allow"

---

## 🗺️ Offline Maps Setup (Optional)

For complete offline functionality, see: [OFFLINE_MAPS_SETUP.md](OFFLINE_MAPS_SETUP.md)

### Quick Offline Setup:

1. **Install TileServer GL:**
   ```bash
   npm install -g tileserver-gl
   ```

2. **Download Saudi Arabia Map Data:**
   ```bash
   # Download from OpenStreetMap
   wget https://download.geofabrik.de/asia/saudi-arabia-latest.osm.pbf
   ```

3. **Convert to MBTiles:**
   ```bash
   # Use tippecanoe or other tools
   tippecanoe -o saudi-arabia.mbtiles saudi-arabia-latest.osm.pbf
   ```

4. **Start Tile Server:**
   ```bash
   tileserver-gl saudi-arabia.mbtiles
   ```

---

## 🔧 Troubleshooting

### Common Issues

#### 1. "Node.js not found"
**Solution:**
- Reinstall Node.js from [nodejs.org](https://nodejs.org/)
- Restart your terminal/command prompt
- Check PATH environment variable

#### 2. "npm install fails"
**Solutions:**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules
npm install

# Use different registry
npm config set registry https://registry.npmjs.org/
```

#### 3. "Port 3000 already in use"
**Solutions:**
```bash
# Windows - Find and kill process
netstat -ano | findstr :3000
taskkill /PID [PID] /F

# Linux/macOS - Find and kill process
lsof -i :3000
kill -9 [PID]

# Or change port in server.js
```

#### 4. "Permission denied"
**Solutions:**
```bash
# Linux/macOS - Run with sudo
sudo npm install

# Or fix npm permissions
npm config set prefix ~/.npm-global
export PATH=~/.npm-global/bin:$PATH
```

#### 5. "Database locked"
**Solution:**
```bash
# Stop the server (Ctrl+C)
# Delete database files
rm daleel.db daleel.db-shm daleel.db-wal
# Restart server
npm start
```

### Performance Issues

#### Slow Loading:
- Check internet connection
- Clear browser cache (Ctrl+Shift+Delete)
- Restart the server

#### High Memory Usage:
- Close other applications
- Restart the browser
- Use latest browser version

---

## 📊 System Monitoring

### Check System Resources

#### Windows:
```cmd
# Check memory usage
tasklist /fi "imagename eq node.exe"

# Check port usage
netstat -an | findstr :3000
```

#### Linux/macOS:
```bash
# Check memory usage
ps aux | grep node

# Check port usage
netstat -tulpn | grep :3000

# Check disk space
df -h
```

---

## 🔄 Updates and Maintenance

### Update Node.js:
1. Download latest version from [nodejs.org](https://nodejs.org/)
2. Install over existing version
3. Verify: `node --version`

### Update Project:
```bash
# If using Git
git pull

# Update dependencies
npm update

# Restart server
npm start
```

### Backup Data:
```bash
# Backup database
cp daleel.db daleel.db.backup

# Export locations
# Use the export feature in the web interface
```

---

## 🌍 Multi-Language Support

### Change System Language:
Currently, the system supports Arabic interface. To add other languages:

1. Edit `public/index.html` for interface text
2. Edit `public/js/app.js` for JavaScript messages
3. Edit `public/css/style.css` for RTL/LTR layout

---

## 📞 Support

### Getting Help:
1. Check [FAQ.md](FAQ.md) for common questions
2. Review [USAGE_GUIDE.md](USAGE_GUIDE.md) for detailed usage
3. Open an issue on GitHub
4. Check system logs for error messages

### Log Files:
- **Application logs**: Check terminal output
- **Browser logs**: Press F12 → Console tab
- **System logs**: 
  - Windows: Event Viewer
  - Linux: `/var/log/syslog`
  - macOS: Console app

---

## ✅ Installation Checklist

- [ ] Node.js 14+ installed
- [ ] npm working correctly
- [ ] Project files downloaded/extracted
- [ ] Dependencies installed (`npm install`)
- [ ] Server starts successfully (`npm start`)
- [ ] Browser can access `http://localhost:3000`
- [ ] Location services enabled in browser
- [ ] Firewall configured (if needed)
- [ ] Network access working (if needed)

---

## 🎯 Next Steps

After successful installation:

1. **Read the User Guide**: [USAGE_GUIDE.md](USAGE_GUIDE.md)
2. **Import Sample Data**: Use `sample-data.json`
3. **Configure Offline Maps**: [OFFLINE_MAPS_SETUP.md](OFFLINE_MAPS_SETUP.md)
4. **Explore Features**: Try adding locations, routing, tracking

---

<div align="center">

**🎉 Installation Complete!**

**Enjoy using Bousla Navigation System**

</div>
