#!/bin/bash

# Bousla Navigation System Setup Script

echo "=========================================="
echo "      Bousla Navigation System Setup"
echo "=========================================="
echo ""

# Check if Node.js is installed
echo "🔍 Checking Node.js..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first from:"
    echo "   https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node -v)
echo "✅ Node.js installed: $NODE_VERSION"
echo ""

# Check if npm is available
echo "🔍 Checking npm..."
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed"
    exit 1
fi

NPM_VERSION=$(npm -v)
echo "✅ npm installed: $NPM_VERSION"
echo ""

# Install dependencies
echo "📦 Installing required dependencies..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi
echo ""

# Create required directories
echo "📁 Creating directories..."
mkdir -p public/css
mkdir -p public/js
mkdir -p public/tiles
mkdir -p data

echo "✅ Directories created"
echo ""

# Check for essential files
echo "🔍 Checking essential files..."

FILES=(
    "server.js"
    "public/index.html"
    "public/css/style.css"
    "public/js/app.js"
    "public/js/saudi-cities.js"
)

ALL_FILES_EXIST=true
for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file not found"
        ALL_FILES_EXIST=false
    fi
done

if [ "$ALL_FILES_EXIST" = false ]; then
    echo ""
    echo "❌ Some essential files are missing"
    exit 1
fi
echo ""

# Setup database
echo "💾 Setting up database..."
if [ -f "bousla.db" ]; then
    echo "⚠️  Database already exists"
    read -p "Do you want to delete it and create a new one? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm bousla.db
        echo "✅ Old database deleted"
    fi
else
    echo "✅ Database will be created on first run"
fi
echo ""

# Display setup information
echo "=========================================="
echo "   ✅ Setup completed successfully!"
echo "=========================================="
echo ""
echo "📋 Next steps:"
echo ""
echo "1️⃣  To start the server:"
echo "   npm start"
echo ""
echo "2️⃣  Open your browser at:"
echo "   http://localhost:3000"
echo ""
echo "3️⃣  For network access:"
echo "   Get your IP address:"
if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    IP=$(hostname -I | awk '{print $1}')
    if [ ! -z "$IP" ]; then
        echo "   http://$IP:3000"
    else
        echo "   Use: ifconfig or ip addr"
    fi
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    echo "   Use: ipconfig"
fi
echo ""
echo "4️⃣  For offline maps setup:"
echo "   See file: OFFLINE_MAPS_SETUP.md"
echo ""
echo "5️⃣  For complete user guide:"
echo "   See file: USAGE_GUIDE.md"
echo ""
echo "=========================================="
echo "   🧭 Enjoy using Bousla Navigation!"
echo "=========================================="

