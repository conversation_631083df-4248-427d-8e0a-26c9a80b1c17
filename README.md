# 🧭 بوصلة - نظام ملاحة أوفلاين للمملكة العربية السعودية

<div align="center">

![Version](https://img.shields.io/badge/version-1.0.0-green.svg)
![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Node](https://img.shields.io/badge/node-%3E%3D14.0.0-brightgreen.svg)

**نظام ملاحة متكامل يعمل بدون إنترنت مصمم خصيصاً للمملكة العربية السعودية**

[التثبيت](#-التثبيت-السريع) • [الميزات](#-المميزات) • [الاستخدام](#-الاستخدام) • [التوثيق](#-التوثيق)

</div>

---

## 📋 نظرة عامة

**دليل** هو نظام ملاحة أوفلاين كامل مصمم للعمل بدون اتصال بالإنترنت. يوفر جميع الميزات الأساسية لأنظمة الملاحة الحديثة مثل Garmin وخرائط Google، ولكن بشكل محلي بالكامل على جهازك أو شبكتك الداخلية.

### ✨ لماذا دليل؟

- ✅ **خصوصية كاملة**: جميع بياناتك تبقى على جهازك
- ✅ **لا يحتاج إنترنت**: يعمل بشكل كامل أوفلاين
- ✅ **مصمم للسعودية**: يحتوي على 150+ مدينة سعودية
- ✅ **واجهة عربية**: تصميم عربي كامل من اليمين لليسار
- ✅ **مفتوح المصدر**: مجاني ومفتوح للتطوير
- ✅ **سهل الاستخدام**: واجهة بسيطة وأنيقة

---

## 🎯 المميزات

### 🗺️ الخرائط
- خريطة المملكة العربية السعودية كاملة
- دعم التكبير والتصغير والتنقل
- إمكانية العمل أوفلاين 100%
- خرائط OSM عالية الجودة

### 📍 إدارة المواقع
- حفظ مواقعك المفضلة (المنزل، العمل، إلخ)
- تسمية مخصصة لكل موقع
- إضافة أوصاف تفصيلية
- عرض وتعديل وحذف المواقع
- تحديد المواقع من الخريطة مباشرة

### 🧭 نظام التوجيه
- إنشاء مسارات من موقعك الحالي إلى أي وجهة
- حساب المسافة والوقت المتوقع
- عرض المسار على الخريطة بشكل واضح
- حفظ المسارات المهمة

### 📱 تتبع GPS
- تتبع موقعك الحالي في الوقت الفعلي
- عرض السرعة والاتجاه
- تحديث مستمر للموقع
- حفظ سجل التتبع

### 🔍 البحث المتقدم
- البحث في المواقع المحفوظة
- البحث في 150+ مدينة سعودية
- نتائج فورية ودقيقة
- الانتقال السريع للنتائج

### 💾 إدارة البيانات
- تصدير جميع مواقعك بصيغة JSON
- استيراد المواقع من ملفات سابقة
- نسخ احتياطي سهل
- قاعدة بيانات SQLite محلية

### 🎨 التصميم
- واجهة عربية كاملة (RTL)
- تصميم أخضر داكن أنيق
- متجاوب مع جميع الأجهزة
- رسوم متحركة سلسة

---

## 🚀 التثبيت السريع

### المتطلبات الأساسية
- Node.js 14 أو أحدث ([تحميل](https://nodejs.org/))
- متصفح حديث (Chrome, Firefox, Safari, Edge)

📖 **للمتطلبات التفصيلية**: راجع [SYSTEM_REQUIREMENTS.md](SYSTEM_REQUIREMENTS.md)

### الطريقة 1: استخدام سكريبت الإعداد (موصى به)

#### Windows:
```bash
# قم بتشغيل ملف الإعداد
setup.bat
```

#### Linux/Mac:
```bash
# امنح صلاحيات التنفيذ
chmod +x setup.sh

# قم بتشغيل ملف الإعداد
./setup.sh
```

### الطريقة 2: التثبيت اليدوي

```bash
# 1. تثبيت المكتبات
npm install

# 2. تشغيل الخادم
npm start
```

### 3. افتح المتصفح

افتح المتصفح على: **http://localhost:3000**

### 📁 نقل المشروع لكمبيوتر آخر

راجع [TRANSFER_GUIDE.md](TRANSFER_GUIDE.md) لتعليمات مفصلة حول نقل المشروع

---

## 💻 الاستخدام

### البدء السريع

1. **تحديد موقعك الحالي**
   - اضغط على زر 🎯 في الخريطة
   - امنح المتصفح إذن الوصول للموقع

2. **إضافة موقع مفضل**
   - افتح القائمة الجانبية
   - اضغط "إضافة موقع"
   - أدخل الاسم والإحداثيات أو حدد من الخريطة

3. **التوجيه إلى موقع**
   - افتح تبويب "التوجيه"
   - اختر الوجهة من المواقع المحفوظة
   - اضغط "ابدأ التوجيه"

4. **تتبع موقعك**
   - افتح تبويب "التتبع"
   - اضغط "بدء التتبع"
   - راقب موقعك وسرعتك في الوقت الفعلي

### للاستخدام على الشبكة المحلية

1. احصل على عنوان IP لجهازك:
   ```bash
   # Windows
   ipconfig

   # Linux/Mac
   ifconfig
   ```

2. على الأجهزة الأخرى، افتح:
   ```
   http://[YOUR-IP]:3000
   ```

---

## 📚 التوثيق

### ملفات التوثيق الكاملة

- 📖 **[USAGE_GUIDE.md](USAGE_GUIDE.md)** - دليل الاستخدام الشامل
- 🗺️ **[OFFLINE_MAPS_SETUP.md](OFFLINE_MAPS_SETUP.md)** - إعداد الخرائط الأوفلاين
- 🏗️ **[ARCHITECTURE.md](ARCHITECTURE.md)** - بنية المشروع التقنية
- 💻 **[SYSTEM_REQUIREMENTS.md](SYSTEM_REQUIREMENTS.md)** - متطلبات النظام والتثبيت
- 📁 **[TRANSFER_GUIDE.md](TRANSFER_GUIDE.md)** - دليل نقل المشروع
- 📦 **[sample-data.json](sample-data.json)** - بيانات تجريبية للمواقع

---

## 🛠️ البنية التقنية

### Backend (الخادم)
- **Node.js** - بيئة التشغيل
- **Express** - إطار عمل الخادم
- **better-sqlite3** - قاعدة البيانات
- **CORS** - دعم الطلبات المتعددة

### Frontend (الواجهة)
- **HTML5** - الهيكل
- **CSS3** - التصميم
- **JavaScript (ES6+)** - المنطق
- **Leaflet.js** - مكتبة الخرائط
- **Leaflet Routing Machine** - التوجيه
- **Font Awesome** - الأيقونات

### Database (قاعدة البيانات)
- **SQLite** - قاعدة بيانات محلية
- 4 جداول رئيسية:
  - `locations` - المواقع المحفوظة
  - `routes` - المسارات
  - `tracking_history` - سجل التتبع
  - `settings` - الإعدادات

---

## 📁 هيكل المشروع

```
DLYL/
├── server.js              # الخادم الرئيسي
├── package.json           # تعريف المشروع
├── daleel.db             # قاعدة البيانات (يتم إنشاؤها تلقائياً)
│
├── public/               # الواجهة الأمامية
│   ├── index.html       # الصفحة الرئيسية
│   ├── css/
│   │   └── style.css    # التصميم
│   └── js/
│       ├── app.js       # المنطق الرئيسي
│       └── saudi-cities.js  # قاعدة بيانات المدن
│
├── setup.sh             # سكريبت الإعداد (Linux/Mac)
├── setup.bat            # سكريبت الإعداد (Windows)
│
└── docs/                # التوثيق
    ├── README.md
    ├── USAGE_GUIDE.md
    ├── OFFLINE_MAPS_SETUP.md
    └── ARCHITECTURE.md
```

---

## 🌐 المتصفحات المدعومة

| المتصفح | الإصدار | الدعم |
|---------|---------|-------|
| Chrome  | 90+     | ✅ كامل |
| Firefox | 88+     | ✅ كامل |
| Safari  | 14+     | ✅ كامل |
| Edge    | 90+     | ✅ كامل |

---

## 🔧 حل المشاكل الشائعة

### الخريطة لا تظهر
```bash
# تأكد من تشغيل الخادم
npm start

# تحقق من المنفذ 3000
netstat -an | grep 3000
```

### المتصفح لا يطلب إذن الموقع
- تأكد من استخدام `localhost` أو `https`
- تحقق من إعدادات المتصفح

### قاعدة البيانات لا تعمل
```bash
# احذف قاعدة البيانات وأعد التشغيل
rm daleel.db
npm start
```

---

## 🎓 أمثلة الاستخدام

### مثال 1: حفظ موقع المنزل
```javascript
// يمكنك استيراد هذا الموقع عبر واجهة الاستيراد
{
  "name": "المنزل",
  "latitude": 24.7136,
  "longitude": 46.6753,
  "description": "منزلي في الرياض"
}
```

### مثال 2: استيراد مواقع متعددة
```bash
# استخدم ملف sample-data.json المرفق
# يحتوي على 20 موقعاً سياحياً في السعودية
```

---

## 🤝 المساهمة

نرحب بمساهماتكم! إليك كيفية المساهمة:

1. Fork المشروع
2. أنشئ فرعاً للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. افتح Pull Request

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 🙏 شكر وتقدير

- **OpenStreetMap** - بيانات الخرائط
- **Leaflet.js** - مكتبة الخرائط
- **Font Awesome** - الأيقونات
- **المجتمع العربي** - الدعم والتشجيع

---

## 📞 الدعم والتواصل

- 📧 للاستفسارات: افتح Issue على GitHub
- 📖 للتوثيق: راجع ملفات التوثيق
- 🐛 للإبلاغ عن مشاكل: افتح Issue

---

## 🗓️ خارطة الطريق

### الإصدار 1.0 (الحالي) ✅
- [x] نظام الخرائط الأساسي
- [x] إدارة المواقع
- [x] التوجيه والمسارات
- [x] تتبع GPS
- [x] البحث
- [x] تصدير/استيراد البيانات

### الإصدار 1.1 (قريباً)
- [ ] دعم الخرائط الأوفلاين الكامل
- [ ] الملاحة الصوتية
- [ ] وضع الليل
- [ ] Progressive Web App (PWA)

### الإصدار 2.0 (مستقبلاً)
- [ ] دعم متعدد المستخدمين
- [ ] نقاط الاهتمام (POI)
- [ ] التقارير والإحصائيات
- [ ] المزامنة السحابية (اختيارية)

---

<div align="center">

**صُنع بـ ❤️ في المملكة العربية السعودية**

⭐ إذا أعجبك المشروع، لا تنسَ إعطائه نجمة!

</div>

