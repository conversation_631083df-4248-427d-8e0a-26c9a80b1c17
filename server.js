const express = require('express');
const path = require('path');
const cors = require('cors');
const Database = require('better-sqlite3');

const app = express();
const PORT = 3000;

// إعداد قاعدة البيانات
const db = new Database('bousla.db');

// إنشاء الجداول
db.exec(`
  CREATE TABLE IF NOT EXISTS locations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    latitude REAL NOT NULL,
    longitude REAL NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );

  CREATE TABLE IF NOT EXISTS routes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    start_lat REAL NOT NULL,
    start_lng REAL NOT NULL,
    end_lat REAL NOT NULL,
    end_lng REAL NOT NULL,
    distance REAL,
    duration REAL,
    route_data TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );

  CREATE TABLE IF NOT EXISTS settings (
    key TEXT PRIMARY KEY,
    value TEXT
  );

  CREATE TABLE IF NOT EXISTS tracking_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    latitude REAL NOT NULL,
    longitude REAL NOT NULL,
    speed REAL,
    heading REAL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
  );
`);

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// ============ API Routes ============

// الحصول على جميع المواقع
app.get('/api/locations', (req, res) => {
  try {
    const locations = db.prepare('SELECT * FROM locations ORDER BY created_at DESC').all();
    res.json({ success: true, data: locations });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// إضافة موقع جديد
app.post('/api/locations', (req, res) => {
  try {
    const { name, latitude, longitude, description } = req.body;
    const stmt = db.prepare('INSERT INTO locations (name, latitude, longitude, description) VALUES (?, ?, ?, ?)');
    const result = stmt.run(name, latitude, longitude, description || '');
    res.json({ success: true, id: result.lastInsertRowid });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// تحديث موقع
app.put('/api/locations/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { name, latitude, longitude, description } = req.body;
    const stmt = db.prepare('UPDATE locations SET name = ?, latitude = ?, longitude = ?, description = ? WHERE id = ?');
    stmt.run(name, latitude, longitude, description || '', id);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// حذف موقع
app.delete('/api/locations/:id', (req, res) => {
  try {
    const { id } = req.params;
    const stmt = db.prepare('DELETE FROM locations WHERE id = ?');
    stmt.run(id);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// حفظ مسار
app.post('/api/routes', (req, res) => {
  try {
    const { name, start_lat, start_lng, end_lat, end_lng, distance, duration, route_data } = req.body;
    const stmt = db.prepare('INSERT INTO routes (name, start_lat, start_lng, end_lat, end_lng, distance, duration, route_data) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
    const result = stmt.run(name, start_lat, start_lng, end_lat, end_lng, distance, duration, JSON.stringify(route_data));
    res.json({ success: true, id: result.lastInsertRowid });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// الحصول على المسارات
app.get('/api/routes', (req, res) => {
  try {
    const routes = db.prepare('SELECT * FROM routes ORDER BY created_at DESC').all();
    res.json({ success: true, data: routes });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// حفظ نقطة تتبع
app.post('/api/tracking', (req, res) => {
  try {
    const { latitude, longitude, speed, heading } = req.body;
    const stmt = db.prepare('INSERT INTO tracking_history (latitude, longitude, speed, heading) VALUES (?, ?, ?, ?)');
    stmt.run(latitude, longitude, speed || null, heading || null);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// الحصول على سجل التتبع
app.get('/api/tracking', (req, res) => {
  try {
    const limit = req.query.limit || 100;
    const tracking = db.prepare('SELECT * FROM tracking_history ORDER BY timestamp DESC LIMIT ?').all(limit);
    res.json({ success: true, data: tracking });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// تصدير المواقع
app.get('/api/export/locations', (req, res) => {
  try {
    const locations = db.prepare('SELECT * FROM locations').all();
    res.json({ success: true, data: locations });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// استيراد المواقع
app.post('/api/import/locations', (req, res) => {
  try {
    const { locations } = req.body;
    const stmt = db.prepare('INSERT INTO locations (name, latitude, longitude, description) VALUES (?, ?, ?, ?)');
    
    const insertMany = db.transaction((locs) => {
      for (const loc of locs) {
        stmt.run(loc.name, loc.latitude, loc.longitude, loc.description || '');
      }
    });
    
    insertMany(locations);
    res.json({ success: true, count: locations.length });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// الإعدادات
app.get('/api/settings/:key', (req, res) => {
  try {
    const { key } = req.params;
    const setting = db.prepare('SELECT value FROM settings WHERE key = ?').get(key);
    res.json({ success: true, value: setting ? setting.value : null });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/settings', (req, res) => {
  try {
    const { key, value } = req.body;
    const stmt = db.prepare('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)');
    stmt.run(key, value);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
app.listen(PORT, () => {
  console.log(`🧭 خادم بوصلة يعمل على http://localhost:${PORT}`);
  console.log(`📍 نظام الملاحة الأوفلاين جاهز`);
});

// إغلاق قاعدة البيانات عند إيقاف الخادم
process.on('SIGINT', () => {
  db.close();
  process.exit(0);
});

