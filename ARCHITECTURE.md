# بنية نظام دليل للملاحة الأوفلاين

## 📐 نظرة عامة على البنية

نظام "دليل" هو تطبيق ويب كامل للملاحة الأوفلاين مصمم خصيصاً للمملكة العربية السعودية. يعتمد على بنية Client-Server بسيطة وفعالة.

```
┌─────────────────────────────────────────────────────────┐
│                    المتصفح (Client)                      │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐  │
│  │   HTML/CSS   │  │  JavaScript  │  │   Leaflet    │  │
│  │  (الواجهة)   │  │   (المنطق)   │  │  (الخرائط)   │  │
│  └──────────────┘  └──────────────┘  └──────────────┘  │
└─────────────────────────────────────────────────────────┘
                          ↕ HTTP/REST API
┌─────────────────────────────────────────────────────────┐
│                   الخادم (Server)                        │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐  │
│  │   Node.js    │  │   Express    │  │   SQLite     │  │
│  │   (Runtime)  │  │   (Server)   │  │  (Database)  │  │
│  └──────────────┘  └──────────────┘  └──────────────┘  │
└─────────────────────────────────────────────────────────┘
```

---

## 📁 هيكل المشروع

```
DLYL/
├── server.js                    # الخادم الرئيسي (Node.js + Express)
├── package.json                 # تعريف المشروع والمكتبات
├── daleel.db                    # قاعدة بيانات SQLite (يتم إنشاؤها تلقائياً)
│
├── public/                      # الملفات العامة (الواجهة الأمامية)
│   ├── index.html              # الصفحة الرئيسية
│   │
│   ├── css/
│   │   └── style.css           # التصميم الكامل للموقع
│   │
│   ├── js/
│   │   ├── app.js              # المنطق الرئيسي للتطبيق
│   │   └── saudi-cities.js     # قاعدة بيانات المدن السعودية
│   │
│   └── tiles/                  # مجلد الخرائط الأوفلاين (اختياري)
│
├── setup.sh                     # سكريبت الإعداد (Linux/Mac)
├── setup.bat                    # سكريبت الإعداد (Windows)
│
├── README.md                    # معلومات المشروع
├── USAGE_GUIDE.md              # دليل الاستخدام
├── OFFLINE_MAPS_SETUP.md       # دليل إعداد الخرائط الأوفلاين
├── ARCHITECTURE.md             # هذا الملف - بنية المشروع
└── sample-data.json            # بيانات تجريبية للمواقع
```

---

## 🔧 المكونات الرئيسية

### 1. الخادم (Backend)

#### server.js
- **الوظيفة**: خادم Node.js يستخدم Express
- **المسؤوليات**:
  - إدارة قاعدة البيانات SQLite
  - توفير REST API للعمليات CRUD
  - تقديم الملفات الثابتة (HTML, CSS, JS)
  - معالجة طلبات المواقع والمسارات والتتبع

#### قاعدة البيانات (SQLite)
**الجداول**:

1. **locations** - المواقع المحفوظة
   ```sql
   - id (INTEGER PRIMARY KEY)
   - name (TEXT)
   - latitude (REAL)
   - longitude (REAL)
   - description (TEXT)
   - created_at (DATETIME)
   ```

2. **routes** - المسارات المحفوظة
   ```sql
   - id (INTEGER PRIMARY KEY)
   - name (TEXT)
   - start_lat, start_lng (REAL)
   - end_lat, end_lng (REAL)
   - distance (REAL)
   - duration (REAL)
   - route_data (TEXT/JSON)
   - created_at (DATETIME)
   ```

3. **tracking_history** - سجل التتبع
   ```sql
   - id (INTEGER PRIMARY KEY)
   - latitude, longitude (REAL)
   - speed, heading (REAL)
   - timestamp (DATETIME)
   ```

4. **settings** - الإعدادات
   ```sql
   - key (TEXT PRIMARY KEY)
   - value (TEXT)
   ```

---

### 2. الواجهة الأمامية (Frontend)

#### index.html
- **الوظيفة**: الهيكل الأساسي للصفحة
- **المكونات**:
  - Header (الشعار والأزرار)
  - Sidebar (القائمة الجانبية)
  - Map Container (حاوية الخريطة)
  - Modals (النوافذ المنبثقة)

#### style.css
- **الوظيفة**: التصميم الكامل
- **المميزات**:
  - تصميم عربي (RTL)
  - ألوان خضراء داكنة
  - تصميم متجاوب (Responsive)
  - رسوم متحركة (Animations)

#### app.js
- **الوظيفة**: المنطق الرئيسي للتطبيق
- **الوحدات**:
  - إدارة الخريطة (Leaflet)
  - إدارة المواقع (CRUD)
  - نظام التوجيه (Routing)
  - نظام التتبع (GPS Tracking)
  - البحث والإشعارات

#### saudi-cities.js
- **الوظيفة**: قاعدة بيانات المدن السعودية
- **المحتوى**: 150+ مدينة مع الإحداثيات
- **الدوال**:
  - `searchCity()` - البحث عن مدينة
  - `getNearestCity()` - أقرب مدينة
  - `getCitiesByRegion()` - مدن منطقة معينة

---

## 🔄 تدفق البيانات (Data Flow)

### 1. إضافة موقع جديد

```
المستخدم → نموذج HTML → JavaScript (app.js)
    ↓
POST /api/locations
    ↓
Express Handler → SQLite INSERT
    ↓
Response JSON ← Server
    ↓
تحديث الواجهة ← JavaScript
```

### 2. التوجيه

```
المستخدم يختار وجهة → JavaScript
    ↓
Geolocation API (موقع حالي)
    ↓
Leaflet Routing Machine
    ↓
حساب المسار → عرض على الخريطة
    ↓
POST /api/routes (حفظ المسار)
```

### 3. التتبع

```
بدء التتبع → watchPosition()
    ↓
تحديث مستمر للموقع
    ↓
تحديث الواجهة + تحديث Marker
    ↓
POST /api/tracking (حفظ كل نقطة)
```

---

## 🌐 REST API Endpoints

### المواقع (Locations)
- `GET /api/locations` - جلب جميع المواقع
- `POST /api/locations` - إضافة موقع جديد
- `PUT /api/locations/:id` - تحديث موقع
- `DELETE /api/locations/:id` - حذف موقع

### المسارات (Routes)
- `GET /api/routes` - جلب جميع المسارات
- `POST /api/routes` - حفظ مسار جديد

### التتبع (Tracking)
- `GET /api/tracking` - جلب سجل التتبع
- `POST /api/tracking` - حفظ نقطة تتبع

### البيانات (Data)
- `GET /api/export/locations` - تصدير المواقع
- `POST /api/import/locations` - استيراد المواقع

### الإعدادات (Settings)
- `GET /api/settings/:key` - جلب إعداد
- `POST /api/settings` - حفظ إعداد

---

## 🗺️ نظام الخرائط

### الوضع الحالي (Online)
```javascript
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png')
```

### الوضع الأوفلاين (Offline)
يمكن استبدال المصدر بـ:
- خادم Tiles محلي (TileServer GL)
- ملفات Tiles محلية
- MBTiles files

---

## 🔐 الأمان والخصوصية

### البيانات المحلية
- جميع البيانات مخزنة محلياً في SQLite
- لا يتم إرسال أي بيانات لخوادم خارجية
- الموقع الجغرافي يُستخدم فقط محلياً

### الصلاحيات المطلوبة
- **Geolocation**: لتحديد الموقع الحالي
- **Storage**: لحفظ البيانات المحلية

---

## ⚡ الأداء والتحسينات

### تحسينات الأداء
1. **قاعدة البيانات**:
   - استخدام Indexes على الحقول المهمة
   - Transactions للعمليات المتعددة

2. **الواجهة الأمامية**:
   - تحميل الخرائط بشكل تدريجي (Lazy Loading)
   - تخزين مؤقت للبيانات (Caching)

3. **الشبكة**:
   - ضغط الاستجابات (Compression)
   - تقليل حجم الملفات (Minification)

### استهلاك الموارد
- **الذاكرة**: ~50-100 MB
- **المساحة**: ~10 MB (بدون خرائط أوفلاين)
- **المساحة مع الخرائط**: 2-5 GB (حسب التغطية)

---

## 🔌 التوسعات المستقبلية

### ميزات مقترحة
1. **دعم متعدد المستخدمين**: مشاركة المواقع بين المستخدمين
2. **الملاحة الصوتية**: إرشادات صوتية أثناء القيادة
3. **وضع الليل**: تصميم داكن للاستخدام الليلي
4. **نقاط الاهتمام (POI)**: محطات الوقود، المطاعم، إلخ
5. **التقارير**: تقارير عن المسافات والأوقات
6. **المزامنة**: مزامنة البيانات بين الأجهزة

### تحسينات تقنية
1. **Progressive Web App (PWA)**: للعمل كتطبيق مستقل
2. **Service Workers**: للتخزين المؤقت المتقدم
3. **WebSocket**: للتحديثات الفورية
4. **IndexedDB**: لتخزين بيانات أكبر

---

## 🛠️ التطوير والصيانة

### متطلبات التطوير
- Node.js 14+
- npm 6+
- محرر نصوص (VS Code موصى به)

### الاختبار
```bash
# تشغيل الخادم في وضع التطوير
npm run dev

# اختبار API
curl http://localhost:3000/api/locations
```

### النشر (Deployment)
1. **محلي**: تشغيل على localhost
2. **شبكة محلية**: تشغيل على IP محلي
3. **خادم**: نشر على VPS أو Cloud

---

## 📚 المكتبات المستخدمة

### Backend
- **express**: ^4.18.2 - إطار عمل الخادم
- **better-sqlite3**: ^9.2.2 - قاعدة البيانات
- **cors**: ^2.8.5 - دعم CORS

### Frontend
- **Leaflet**: 1.9.4 - مكتبة الخرائط
- **Leaflet Routing Machine**: 3.2.12 - التوجيه
- **Font Awesome**: 6.4.0 - الأيقونات

---

## 🤝 المساهمة

### إرشادات المساهمة
1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. Commit التغييرات
4. Push إلى الفرع
5. فتح Pull Request

### معايير الكود
- استخدام ES6+ JavaScript
- تعليقات بالعربية للوضوح
- اتباع نمط الكود الموجود
- اختبار جميع التغييرات

---

## 📄 الترخيص

MIT License - مفتوح المصدر للاستخدام الحر

---

## 📞 الدعم

للمساعدة أو الاستفسارات:
- راجع ملف USAGE_GUIDE.md
- راجع ملف OFFLINE_MAPS_SETUP.md
- افتح Issue على GitHub

---

**تم التطوير بـ ❤️ لخدمة المملكة العربية السعودية**

