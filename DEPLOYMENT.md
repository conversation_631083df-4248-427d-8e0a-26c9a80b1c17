# دليل النشر - نظام دليل للملاحة

## 📋 نظرة عامة

هذا الدليل يشرح كيفية نشر نظام "دليل" في بيئات مختلفة.

---

## 🏠 النشر المحلي (Local Deployment)

### الاستخدام على جهاز واحد

```bash
# تثبيت المكتبات
npm install

# تشغيل الخادم
npm start
```

افتح المتصفح على: `http://localhost:3000`

---

## 🌐 النشر على الشبكة المحلية (LAN)

### 1. إعداد الخادم

```bash
# تشغيل الخادم
npm start
```

### 2. الحصول على عنوان IP

#### Windows:
```cmd
ipconfig
```
ابحث عن `IPv4 Address` في قسم `Ethernet adapter` أو `Wireless LAN adapter`

#### Linux/Mac:
```bash
ifconfig
# أو
ip addr show
```

### 3. الوصول من الأجهزة الأخرى

على الأجهزة الأخرى في نفس الشبكة، افتح:
```
http://[YOUR-IP-ADDRESS]:3000
```

مثال: `http://*************:3000`

### 4. فتح المنفذ في الجدار الناري

#### Windows Firewall:
```powershell
# تشغيل PowerShell كمسؤول
New-NetFirewallRule -DisplayName "Daleel Navigation" -Direction Inbound -LocalPort 3000 -Protocol TCP -Action Allow
```

#### Linux (UFW):
```bash
sudo ufw allow 3000/tcp
```

#### Linux (iptables):
```bash
sudo iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
```

---

## 🖥️ النشر على خادم محلي (Local Server)

### استخدام PM2 (موصى به)

PM2 هو مدير عمليات يحافظ على تشغيل التطبيق حتى بعد إعادة تشغيل الخادم.

#### 1. تثبيت PM2
```bash
npm install -g pm2
```

#### 2. تشغيل التطبيق
```bash
pm2 start server.js --name daleel
```

#### 3. حفظ التكوين
```bash
pm2 save
pm2 startup
```

#### 4. أوامر إدارة PM2
```bash
# عرض الحالة
pm2 status

# إيقاف التطبيق
pm2 stop daleel

# إعادة تشغيل التطبيق
pm2 restart daleel

# عرض السجلات
pm2 logs daleel

# حذف التطبيق
pm2 delete daleel
```

---

## 🐳 النشر باستخدام Docker

### 1. إنشاء Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install --production

COPY . .

EXPOSE 3000

CMD ["node", "server.js"]
```

### 2. إنشاء .dockerignore

```
node_modules
npm-debug.log
.git
.gitignore
README.md
*.md
daleel.db
```

### 3. بناء الصورة

```bash
docker build -t daleel-navigation .
```

### 4. تشغيل الحاوية

```bash
docker run -d \
  --name daleel \
  -p 3000:3000 \
  -v $(pwd)/daleel.db:/app/daleel.db \
  daleel-navigation
```

### 5. استخدام Docker Compose

إنشاء ملف `docker-compose.yml`:

```yaml
version: '3.8'

services:
  daleel:
    build: .
    container_name: daleel-navigation
    ports:
      - "3000:3000"
    volumes:
      - ./daleel.db:/app/daleel.db
      - ./public/tiles:/app/public/tiles
    restart: unless-stopped
    environment:
      - NODE_ENV=production
```

تشغيل:
```bash
docker-compose up -d
```

---

## ☁️ النشر على خادم سحابي (Cloud Server)

### النشر على VPS (مثل DigitalOcean, Linode, AWS EC2)

#### 1. الاتصال بالخادم
```bash
ssh user@your-server-ip
```

#### 2. تثبيت Node.js
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

#### 3. نقل الملفات
```bash
# من جهازك المحلي
scp -r DLYL user@your-server-ip:/home/<USER>/
```

أو استخدم Git:
```bash
# على الخادم
git clone https://github.com/your-repo/daleel.git
cd daleel
```

#### 4. تثبيت وتشغيل
```bash
npm install
pm2 start server.js --name daleel
pm2 save
pm2 startup
```

#### 5. إعداد Nginx كـ Reverse Proxy

```bash
# تثبيت Nginx
sudo apt install nginx

# إنشاء ملف التكوين
sudo nano /etc/nginx/sites-available/daleel
```

محتوى الملف:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

تفعيل التكوين:
```bash
sudo ln -s /etc/nginx/sites-available/daleel /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

#### 6. إعداد SSL (اختياري)

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx

# الحصول على شهادة SSL
sudo certbot --nginx -d your-domain.com
```

---

## 🔒 الأمان

### 1. تغيير المنفذ الافتراضي

في `server.js`:
```javascript
const PORT = process.env.PORT || 8080; // بدلاً من 3000
```

### 2. إضافة مصادقة أساسية

```bash
npm install express-basic-auth
```

في `server.js`:
```javascript
const basicAuth = require('express-basic-auth');

app.use(basicAuth({
    users: { 'admin': 'password123' },
    challenge: true
}));
```

### 3. تحديد الوصول بعناوين IP محددة

```javascript
const allowedIPs = ['***********/24'];

app.use((req, res, next) => {
    const clientIP = req.ip;
    // تحقق من IP
    next();
});
```

---

## 📊 المراقبة والصيانة

### 1. مراقبة السجلات

```bash
# باستخدام PM2
pm2 logs daleel

# باستخدام journalctl (systemd)
sudo journalctl -u daleel -f
```

### 2. النسخ الاحتياطي

```bash
# نسخ احتياطي لقاعدة البيانات
cp daleel.db daleel.db.backup-$(date +%Y%m%d)

# نسخ احتياطي تلقائي (cron)
0 2 * * * cp /path/to/daleel.db /path/to/backups/daleel.db.$(date +\%Y\%m\%d)
```

### 3. التحديثات

```bash
# سحب آخر التحديثات
git pull

# تثبيت المكتبات الجديدة
npm install

# إعادة تشغيل التطبيق
pm2 restart daleel
```

---

## 🚀 تحسين الأداء

### 1. تفعيل الضغط (Compression)

```bash
npm install compression
```

في `server.js`:
```javascript
const compression = require('compression');
app.use(compression());
```

### 2. تخزين مؤقت للملفات الثابتة

```javascript
app.use(express.static('public', {
    maxAge: '1d',
    etag: true
}));
```

### 3. استخدام Cluster Mode

```javascript
const cluster = require('cluster');
const os = require('os');

if (cluster.isMaster) {
    const numCPUs = os.cpus().length;
    for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
    }
} else {
    // كود الخادم هنا
}
```

---

## 📱 النشر كتطبيق PWA

### 1. إنشاء manifest.json

```json
{
  "name": "دليل - نظام الملاحة",
  "short_name": "دليل",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#1a5f3f",
  "theme_color": "#1a5f3f",
  "orientation": "portrait",
  "icons": [
    {
      "src": "/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

### 2. إضافة Service Worker

```javascript
// في public/sw.js
self.addEventListener('install', (event) => {
    event.waitUntil(
        caches.open('daleel-v1').then((cache) => {
            return cache.addAll([
                '/',
                '/css/style.css',
                '/js/app.js',
                '/js/saudi-cities.js'
            ]);
        })
    );
});
```

---

## 🔧 استكشاف الأخطاء

### المنفذ مستخدم بالفعل
```bash
# العثور على العملية
lsof -i :3000

# إيقاف العملية
kill -9 [PID]
```

### قاعدة البيانات مقفلة
```bash
# إيقاف جميع العمليات
pm2 stop all

# حذف ملف القفل
rm daleel.db-shm daleel.db-wal

# إعادة التشغيل
pm2 start daleel
```

---

## 📞 الدعم

للمساعدة في النشر:
- راجع ملف README.md
- راجع ملف ARCHITECTURE.md
- افتح Issue على GitHub

---

**ملاحظة**: تأكد من تأمين التطبيق بشكل صحيح قبل نشره على الإنترنت العام.

