<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بوصلة - نظام الملاحة الأوفلاين</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Leaflet Routing Machine CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="logo">
            <i class="fas fa-compass"></i>
            <h1>بوصلة</h1>
        </div>
        <div class="header-actions">
            <button class="btn-icon" id="searchBtn" title="البحث">
                <i class="fas fa-search"></i>
            </button>
            <button class="btn-icon" id="settingsBtn" title="الإعدادات">
                <i class="fas fa-cog"></i>
            </button>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>القائمة الرئيسية</h2>
                <button class="btn-close" id="closeSidebar">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="sidebar-content">
                <!-- Navigation Buttons -->
                <div class="nav-buttons">
                    <button class="nav-btn active" data-tab="locations">
                        <i class="fas fa-map-pin"></i>
                        <span>المواقع المحفوظة</span>
                    </button>
                    <button class="nav-btn" data-tab="routing">
                        <i class="fas fa-route"></i>
                        <span>التوجيه</span>
                    </button>
                    <button class="nav-btn" data-tab="tracking">
                        <i class="fas fa-location-arrow"></i>
                        <span>التتبع</span>
                    </button>
                    <button class="nav-btn" data-tab="history">
                        <i class="fas fa-history"></i>
                        <span>السجل</span>
                    </button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Locations Tab -->
                    <div class="tab-pane active" id="locationsTab">
                        <div class="tab-header">
                            <h3>المواقع المحفوظة</h3>
                            <button class="btn-primary btn-sm" id="addLocationBtn">
                                <i class="fas fa-plus"></i> إضافة موقع
                            </button>
                        </div>
                        <div class="locations-list" id="locationsList">
                            <p class="empty-state">لا توجد مواقع محفوظة</p>
                        </div>
                    </div>

                    <!-- Routing Tab -->
                    <div class="tab-pane" id="routingTab">
                        <div class="tab-header">
                            <h3>التوجيه والمسارات</h3>
                        </div>
                        <div class="routing-form">
                            <div class="form-group">
                                <label>من (موقعك الحالي)</label>
                                <button class="btn-secondary btn-block" id="useCurrentLocation">
                                    <i class="fas fa-crosshairs"></i> استخدام موقعي الحالي
                                </button>
                            </div>
                            <div class="form-group">
                                <label>إلى</label>
                                <select class="form-control" id="destinationSelect">
                                    <option value="">اختر موقعاً محفوظاً</option>
                                </select>
                            </div>
                            <button class="btn-primary btn-block" id="startRoutingBtn">
                                <i class="fas fa-directions"></i> ابدأ التوجيه
                            </button>
                        </div>
                        <div class="route-info" id="routeInfo" style="display: none;">
                            <h4>معلومات المسار</h4>
                            <div class="info-item">
                                <i class="fas fa-road"></i>
                                <span>المسافة: <strong id="routeDistance">-</strong></span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-clock"></i>
                                <span>الوقت المتوقع: <strong id="routeDuration">-</strong></span>
                            </div>
                            <button class="btn-danger btn-block" id="clearRouteBtn">
                                <i class="fas fa-times"></i> إلغاء المسار
                            </button>
                        </div>
                    </div>

                    <!-- Tracking Tab -->
                    <div class="tab-pane" id="trackingTab">
                        <div class="tab-header">
                            <h3>تتبع الموقع</h3>
                        </div>
                        <div class="tracking-controls">
                            <button class="btn-primary btn-block" id="startTrackingBtn">
                                <i class="fas fa-play"></i> بدء التتبع
                            </button>
                            <button class="btn-danger btn-block" id="stopTrackingBtn" style="display: none;">
                                <i class="fas fa-stop"></i> إيقاف التتبع
                            </button>
                        </div>
                        <div class="tracking-info" id="trackingInfo">
                            <div class="info-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>خط العرض: <strong id="currentLat">-</strong></span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>خط الطول: <strong id="currentLng">-</strong></span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>السرعة: <strong id="currentSpeed">-</strong></span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-compass"></i>
                                <span>الاتجاه: <strong id="currentHeading">-</strong></span>
                            </div>
                        </div>
                    </div>

                    <!-- History Tab -->
                    <div class="tab-pane" id="historyTab">
                        <div class="tab-header">
                            <h3>السجل</h3>
                        </div>
                        <div class="history-list" id="historyList">
                            <p class="empty-state">لا يوجد سجل</p>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Map Container -->
        <main class="map-container">
            <div id="map"></div>
            
            <!-- Floating Action Button -->
            <button class="fab" id="menuBtn">
                <i class="fas fa-bars"></i>
            </button>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="quick-btn" id="locateBtn" title="موقعي الحالي">
                    <i class="fas fa-crosshairs"></i>
                </button>
                <button class="quick-btn" id="zoomInBtn" title="تكبير">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="quick-btn" id="zoomOutBtn" title="تصغير">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </main>
    </div>

    <!-- Add Location Modal -->
    <div class="modal" id="addLocationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة موقع جديد</h3>
                <button class="btn-close" id="closeAddLocationModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addLocationForm">
                    <div class="form-group">
                        <label>اسم الموقع *</label>
                        <input type="text" class="form-control" id="locationName" required placeholder="مثال: المنزل، العمل">
                    </div>
                    <div class="form-group">
                        <label>الوصف</label>
                        <textarea class="form-control" id="locationDescription" rows="3" placeholder="وصف اختياري للموقع"></textarea>
                    </div>
                    <div class="form-group">
                        <label>خط العرض *</label>
                        <input type="number" step="any" class="form-control" id="locationLat" required>
                    </div>
                    <div class="form-group">
                        <label>خط الطول *</label>
                        <input type="number" step="any" class="form-control" id="locationLng" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" id="useMapLocationBtn">
                            <i class="fas fa-map-marker-alt"></i> استخدام موقع من الخريطة
                        </button>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Search Modal -->
    <div class="modal" id="searchModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>البحث عن موقع</h3>
                <button class="btn-close" id="closeSearchModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="search-box">
                    <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن مدينة أو منطقة...">
                    <button class="btn-primary" id="searchSubmitBtn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="search-results" id="searchResults"></div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>الإعدادات</h3>
                <button class="btn-close" id="closeSettingsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h4>البيانات</h4>
                    <button class="btn-secondary btn-block" id="exportDataBtn">
                        <i class="fas fa-download"></i> تصدير المواقع
                    </button>
                    <button class="btn-secondary btn-block" id="importDataBtn">
                        <i class="fas fa-upload"></i> استيراد المواقع
                    </button>
                    <input type="file" id="importFileInput" accept=".json" style="display: none;">
                </div>
                <div class="settings-section">
                    <h4>معلومات</h4>
                    <p>نظام دليل للملاحة الأوفلاين</p>
                    <p>الإصدار: 1.0.0</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    <script src="js/saudi-cities.js"></script>
    <script src="js/app.js"></script>
</body>
</html>

