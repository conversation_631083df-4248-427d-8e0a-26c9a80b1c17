// ========== Global Variables ==========
let map;
let currentLocationMarker;
let userMarker;
let routingControl;
let trackingInterval;
let isTracking = false;
let savedLocations = [];
let currentRoute = null;

// ========== Initialize App ==========
document.addEventListener('DOMContentLoaded', () => {
    initMap();
    loadLocations();
    initEventListeners();
    initTabs();
});

// ========== Map Initialization ==========
function initMap() {
    // Initialize map centered on Saudi Arabia
    map = L.map('map').setView([24.7136, 46.6753], 6);

    // Add OpenStreetMap tiles (offline tiles should be configured separately)
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 18,
        minZoom: 5
    }).addTo(map);

    // Add click event to map for adding locations
    map.on('click', (e) => {
        if (window.selectingLocation) {
            document.getElementById('locationLat').value = e.latlng.lat.toFixed(6);
            document.getElementById('locationLng').value = e.latlng.lng.toFixed(6);
            
            // Add temporary marker
            if (currentLocationMarker) {
                map.removeLayer(currentLocationMarker);
            }
            currentLocationMarker = L.marker(e.latlng).addTo(map);
            window.selectingLocation = false;
        }
    });
}

// ========== Event Listeners ==========
function initEventListeners() {
    // Sidebar toggle
    document.getElementById('menuBtn').addEventListener('click', toggleSidebar);
    document.getElementById('closeSidebar').addEventListener('click', toggleSidebar);

    // Quick actions
    document.getElementById('locateBtn').addEventListener('click', locateUser);
    document.getElementById('zoomInBtn').addEventListener('click', () => map.zoomIn());
    document.getElementById('zoomOutBtn').addEventListener('click', () => map.zoomOut());

    // Add location
    document.getElementById('addLocationBtn').addEventListener('click', openAddLocationModal);
    document.getElementById('closeAddLocationModal').addEventListener('click', closeAddLocationModal);
    document.getElementById('addLocationForm').addEventListener('submit', saveLocation);
    document.getElementById('useMapLocationBtn').addEventListener('click', selectFromMap);

    // Search
    document.getElementById('searchBtn').addEventListener('click', openSearchModal);
    document.getElementById('closeSearchModal').addEventListener('click', closeSearchModal);
    document.getElementById('searchSubmitBtn').addEventListener('click', performSearch);

    // Settings
    document.getElementById('settingsBtn').addEventListener('click', openSettingsModal);
    document.getElementById('closeSettingsModal').addEventListener('click', closeSettingsModal);
    document.getElementById('exportDataBtn').addEventListener('click', exportData);
    document.getElementById('importDataBtn').addEventListener('click', () => {
        document.getElementById('importFileInput').click();
    });
    document.getElementById('importFileInput').addEventListener('change', importData);

    // Routing
    document.getElementById('useCurrentLocation').addEventListener('click', setCurrentAsStart);
    document.getElementById('startRoutingBtn').addEventListener('click', startRouting);
    document.getElementById('clearRouteBtn').addEventListener('click', clearRoute);

    // Tracking
    document.getElementById('startTrackingBtn').addEventListener('click', startTracking);
    document.getElementById('stopTrackingBtn').addEventListener('click', stopTracking);
}

// ========== Tabs ==========
function initTabs() {
    const navButtons = document.querySelectorAll('.nav-btn');
    navButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all buttons and panes
            navButtons.forEach(b => b.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            btn.classList.add('active');
            const tabId = btn.dataset.tab + 'Tab';
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// ========== Sidebar ==========
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('active');
}

// ========== Locations ==========
async function loadLocations() {
    try {
        const response = await fetch('/api/locations');
        const result = await response.json();
        
        if (result.success) {
            savedLocations = result.data;
            displayLocations();
            updateDestinationSelect();
        }
    } catch (error) {
        console.error('Error loading locations:', error);
        showNotification('خطأ في تحميل المواقع', 'error');
    }
}

function displayLocations() {
    const locationsList = document.getElementById('locationsList');
    
    if (savedLocations.length === 0) {
        locationsList.innerHTML = '<p class="empty-state">لا توجد مواقع محفوظة</p>';
        return;
    }

    locationsList.innerHTML = savedLocations.map(loc => `
        <div class="location-item" data-id="${loc.id}">
            <div class="location-header">
                <div class="location-name">${loc.name}</div>
                <div class="location-actions">
                    <button onclick="viewLocation(${loc.id})" title="عرض على الخريطة">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="navigateToLocation(${loc.id})" title="التوجيه">
                        <i class="fas fa-directions"></i>
                    </button>
                    <button class="delete-btn" onclick="deleteLocation(${loc.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="location-coords">
                <span><i class="fas fa-map-marker-alt"></i> ${loc.latitude.toFixed(4)}, ${loc.longitude.toFixed(4)}</span>
            </div>
            ${loc.description ? `<div class="location-description">${loc.description}</div>` : ''}
        </div>
    `).join('');
}

function updateDestinationSelect() {
    const select = document.getElementById('destinationSelect');
    select.innerHTML = '<option value="">اختر موقعاً محفوظاً</option>' +
        savedLocations.map(loc => `<option value="${loc.id}">${loc.name}</option>`).join('');
}

function openAddLocationModal() {
    document.getElementById('addLocationModal').classList.add('active');
}

function closeAddLocationModal() {
    document.getElementById('addLocationModal').classList.remove('active');
    document.getElementById('addLocationForm').reset();
    if (currentLocationMarker) {
        map.removeLayer(currentLocationMarker);
        currentLocationMarker = null;
    }
}

function selectFromMap() {
    window.selectingLocation = true;
    closeAddLocationModal();
    showNotification('انقر على الخريطة لتحديد الموقع', 'info');
    
    setTimeout(() => {
        openAddLocationModal();
    }, 100);
}

async function saveLocation(e) {
    e.preventDefault();
    
    const data = {
        name: document.getElementById('locationName').value,
        latitude: parseFloat(document.getElementById('locationLat').value),
        longitude: parseFloat(document.getElementById('locationLng').value),
        description: document.getElementById('locationDescription').value
    };

    try {
        const response = await fetch('/api/locations', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });

        const result = await response.json();
        
        if (result.success) {
            showNotification('تم حفظ الموقع بنجاح', 'success');
            closeAddLocationModal();
            loadLocations();
        }
    } catch (error) {
        console.error('Error saving location:', error);
        showNotification('خطأ في حفظ الموقع', 'error');
    }
}

function viewLocation(id) {
    const location = savedLocations.find(loc => loc.id === id);
    if (location) {
        map.setView([location.latitude, location.longitude], 15);
        
        // Add marker
        const marker = L.marker([location.latitude, location.longitude])
            .addTo(map)
            .bindPopup(`<b>${location.name}</b><br>${location.description || ''}`);
        marker.openPopup();
    }
}

function navigateToLocation(id) {
    document.getElementById('destinationSelect').value = id;
    document.querySelector('[data-tab="routing"]').click();
}

async function deleteLocation(id) {
    if (!confirm('هل أنت متأكد من حذف هذا الموقع؟')) return;

    try {
        const response = await fetch(`/api/locations/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();
        
        if (result.success) {
            showNotification('تم حذف الموقع', 'success');
            loadLocations();
        }
    } catch (error) {
        console.error('Error deleting location:', error);
        showNotification('خطأ في حذف الموقع', 'error');
    }
}

// ========== User Location ==========
function locateUser() {
    if (!navigator.geolocation) {
        showNotification('المتصفح لا يدعم تحديد الموقع', 'error');
        return;
    }

    navigator.geolocation.getCurrentPosition(
        (position) => {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;

            map.setView([lat, lng], 15);

            if (userMarker) {
                map.removeLayer(userMarker);
            }

            userMarker = L.marker([lat, lng], {
                icon: L.divIcon({
                    className: 'user-location-marker',
                    html: '<i class="fas fa-circle" style="color: #007bff; font-size: 20px;"></i>',
                    iconSize: [20, 20]
                })
            }).addTo(map);

            showNotification('تم تحديد موقعك', 'success');
        },
        (error) => {
            showNotification('تعذر تحديد موقعك', 'error');
        }
    );
}

// ========== Routing ==========
function setCurrentAsStart() {
    locateUser();
    showNotification('سيتم استخدام موقعك الحالي كنقطة البداية', 'info');
}

async function startRouting() {
    const destinationId = document.getElementById('destinationSelect').value;

    if (!destinationId) {
        showNotification('الرجاء اختيار وجهة', 'error');
        return;
    }

    const destination = savedLocations.find(loc => loc.id == destinationId);

    if (!navigator.geolocation) {
        showNotification('المتصفح لا يدعم تحديد الموقع', 'error');
        return;
    }

    navigator.geolocation.getCurrentPosition(
        async (position) => {
            const startLat = position.coords.latitude;
            const startLng = position.coords.longitude;

            // Clear existing route
            if (routingControl) {
                map.removeControl(routingControl);
            }

            // Create routing control
            routingControl = L.Routing.control({
                waypoints: [
                    L.latLng(startLat, startLng),
                    L.latLng(destination.latitude, destination.longitude)
                ],
                routeWhileDragging: false,
                showAlternatives: false,
                lineOptions: {
                    styles: [{ color: '#1a5f3f', weight: 6, opacity: 0.8 }]
                },
                createMarker: function(i, waypoint, n) {
                    const marker = L.marker(waypoint.latLng, {
                        draggable: false,
                        icon: L.divIcon({
                            className: 'route-marker',
                            html: i === 0 ? '<i class="fas fa-map-marker-alt" style="color: #007bff; font-size: 30px;"></i>' :
                                           '<i class="fas fa-map-marker-alt" style="color: #dc3545; font-size: 30px;"></i>',
                            iconSize: [30, 30]
                        })
                    });
                    return marker;
                }
            }).addTo(map);

            // Listen for route found
            routingControl.on('routesfound', function(e) {
                const routes = e.routes;
                const summary = routes[0].summary;

                // Display route info
                document.getElementById('routeDistance').textContent =
                    (summary.totalDistance / 1000).toFixed(2) + ' كم';
                document.getElementById('routeDuration').textContent =
                    Math.round(summary.totalTime / 60) + ' دقيقة';
                document.getElementById('routeInfo').style.display = 'block';

                // Save route to database
                saveRoute({
                    name: `مسار إلى ${destination.name}`,
                    start_lat: startLat,
                    start_lng: startLng,
                    end_lat: destination.latitude,
                    end_lng: destination.longitude,
                    distance: summary.totalDistance,
                    duration: summary.totalTime,
                    route_data: routes[0]
                });

                showNotification('تم إنشاء المسار بنجاح', 'success');
            });
        },
        (error) => {
            showNotification('تعذر تحديد موقعك الحالي', 'error');
        }
    );
}

async function saveRoute(routeData) {
    try {
        await fetch('/api/routes', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(routeData)
        });
    } catch (error) {
        console.error('Error saving route:', error);
    }
}

function clearRoute() {
    if (routingControl) {
        map.removeControl(routingControl);
        routingControl = null;
    }
    document.getElementById('routeInfo').style.display = 'none';
    showNotification('تم إلغاء المسار', 'info');
}

// ========== Tracking ==========
function startTracking() {
    if (!navigator.geolocation) {
        showNotification('المتصفح لا يدعم تحديد الموقع', 'error');
        return;
    }

    isTracking = true;
    document.getElementById('startTrackingBtn').style.display = 'none';
    document.getElementById('stopTrackingBtn').style.display = 'block';

    trackingInterval = navigator.geolocation.watchPosition(
        async (position) => {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            const speed = position.coords.speed || 0;
            const heading = position.coords.heading || 0;

            // Update UI
            document.getElementById('currentLat').textContent = lat.toFixed(6);
            document.getElementById('currentLng').textContent = lng.toFixed(6);
            document.getElementById('currentSpeed').textContent = (speed * 3.6).toFixed(1) + ' كم/س';
            document.getElementById('currentHeading').textContent = heading.toFixed(0) + '°';

            // Update marker
            if (userMarker) {
                userMarker.setLatLng([lat, lng]);
            } else {
                userMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: '<i class="fas fa-circle" style="color: #007bff; font-size: 20px;"></i>',
                        iconSize: [20, 20]
                    })
                }).addTo(map);
            }

            // Center map on user
            map.setView([lat, lng]);

            // Save to database
            try {
                await fetch('/api/tracking', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ latitude: lat, longitude: lng, speed, heading })
                });
            } catch (error) {
                console.error('Error saving tracking data:', error);
            }
        },
        (error) => {
            showNotification('خطأ في التتبع', 'error');
            stopTracking();
        },
        {
            enableHighAccuracy: true,
            maximumAge: 0,
            timeout: 5000
        }
    );

    showNotification('بدأ التتبع', 'success');
}

function stopTracking() {
    if (trackingInterval) {
        navigator.geolocation.clearWatch(trackingInterval);
        trackingInterval = null;
    }

    isTracking = false;
    document.getElementById('startTrackingBtn').style.display = 'block';
    document.getElementById('stopTrackingBtn').style.display = 'none';

    showNotification('تم إيقاف التتبع', 'info');
}

// ========== Search ==========
function openSearchModal() {
    document.getElementById('searchModal').classList.add('active');
}

function closeSearchModal() {
    document.getElementById('searchModal').classList.remove('active');
}

async function performSearch() {
    const query = document.getElementById('searchInput').value.trim();

    if (!query) {
        showNotification('الرجاء إدخال نص للبحث', 'error');
        return;
    }

    const resultsDiv = document.getElementById('searchResults');
    let resultsHTML = '';

    // Search in saved locations
    const savedResults = savedLocations.filter(loc =>
        loc.name.includes(query) ||
        (loc.description && loc.description.includes(query))
    );

    if (savedResults.length > 0) {
        resultsHTML += '<h4 style="color: var(--primary-color); margin: 1rem 0;">المواقع المحفوظة</h4>';
        resultsHTML += savedResults.map(loc => `
            <div class="location-item" onclick="viewLocation(${loc.id}); closeSearchModal();" style="cursor: pointer;">
                <div class="location-name">${loc.name}</div>
                <div class="location-coords">
                    <span><i class="fas fa-map-marker-alt"></i> ${loc.latitude.toFixed(4)}, ${loc.longitude.toFixed(4)}</span>
                </div>
            </div>
        `).join('');
    }

    // Search in Saudi cities database
    if (typeof searchCity !== 'undefined') {
        const cityResults = searchCity(query);

        if (cityResults.length > 0) {
            resultsHTML += '<h4 style="color: var(--primary-color); margin: 1rem 0;">المدن السعودية</h4>';
            resultsHTML += cityResults.slice(0, 10).map(city => `
                <div class="location-item" onclick="goToCity(${city.lat}, ${city.lng}, '${city.name}'); closeSearchModal();" style="cursor: pointer;">
                    <div class="location-name">${city.name}</div>
                    <div class="location-coords">
                        <span><i class="fas fa-map-pin"></i> ${city.region}</span>
                        <span><i class="fas fa-map-marker-alt"></i> ${city.lat.toFixed(4)}, ${city.lng.toFixed(4)}</span>
                    </div>
                </div>
            `).join('');
        }
    }

    if (resultsHTML === '') {
        resultsDiv.innerHTML = '<p class="empty-state">لا توجد نتائج</p>';
    } else {
        resultsDiv.innerHTML = resultsHTML;
    }
}

function goToCity(lat, lng, name) {
    map.setView([lat, lng], 12);

    const marker = L.marker([lat, lng])
        .addTo(map)
        .bindPopup(`<b>${name}</b>`);
    marker.openPopup();

    showNotification(`تم الانتقال إلى ${name}`, 'success');
}

// ========== Settings ==========
function openSettingsModal() {
    document.getElementById('settingsModal').classList.add('active');
}

function closeSettingsModal() {
    document.getElementById('settingsModal').classList.remove('active');
}

async function exportData() {
    try {
        const response = await fetch('/api/export/locations');
        const result = await response.json();

        if (result.success) {
            const dataStr = JSON.stringify(result.data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `bousla-locations-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير البيانات بنجاح', 'success');
        }
    } catch (error) {
        console.error('Error exporting data:', error);
        showNotification('خطأ في تصدير البيانات', 'error');
    }
}

async function importData(e) {
    const file = e.target.files[0];

    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (event) => {
        try {
            const locations = JSON.parse(event.target.result);

            const response = await fetch('/api/import/locations', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ locations })
            });

            const result = await response.json();

            if (result.success) {
                showNotification(`تم استيراد ${result.count} موقع بنجاح`, 'success');
                loadLocations();
                closeSettingsModal();
            }
        } catch (error) {
            console.error('Error importing data:', error);
            showNotification('خطأ في استيراد البيانات', 'error');
        }
    };

    reader.readAsText(file);
}

// ========== Notifications ==========
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;

    // Add to body
    document.body.appendChild(notification);

    // Add styles if not exists
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 100px;
                right: 20px;
                background: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                display: flex;
                align-items: center;
                gap: 0.75rem;
                z-index: 3000;
                animation: slideIn 0.3s ease;
            }

            .notification-success { border-right: 4px solid #28a745; }
            .notification-error { border-right: 4px solid #dc3545; }
            .notification-info { border-right: 4px solid #17a2b8; }

            .notification i {
                font-size: 1.5rem;
            }

            .notification-success i { color: #28a745; }
            .notification-error i { color: #dc3545; }
            .notification-info i { color: #17a2b8; }

            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideIn 0.3s ease reverse';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

